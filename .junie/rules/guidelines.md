# Nawart Attendance Backend Controller Guidelines

## Structure and Organization

- Controllers should be organized in namespaced directories based on their purpose (`Frontend`, `Mobile`, `Device`,
  `External`)
- Extend the base `Controller` class which inherits from <PERSON><PERSON>'s `BaseController`
- Use single-action controllers with `__invoke()` method for simple, focused operations
- Use resourceful controllers with standard CRUD methods for entity management (index, show, store, update, destroy)
- Follow PSR-12 coding standards for consistency

## Authorization

- Implement authorization in the constructor using `authorizeResource()` for resource controllers
- Use Laravel's policy mechanisms for fine-grained authorization control
- For non-resource controllers, use specific authorization checks in individual methods

## Documentation

- Use annotations to document API endpoints, parameters, and responses
- Include `@group` and `@subgroup` tags to organize documentation
- Use `@apiResource` and `@apiResourceModel` to reference resource classes
- Document query parameters with `@queryParam` annotations

## Request Handling

- Use Form Request classes for validation and authorization
- Leverage request's validated() method to get clean data
- Use dependency injection to receive request instances
- Separate complex validation logic into dedicated Form Request classes

## Business Logic

- Controllers should be thin and focused on HTTP concerns
- Delegate business logic to dedicated Service classes
- Use Query Builder classes for complex query construction
- Avoid implementing business logic directly in controllers

## Responses

- Always use the project's `ApiResponse` class for consistent API responses
- Wrap resource collections and single resources in appropriate Resource classes
- Include relevant message strings for successful operations
- Use proper HTTP status codes for different response types

## Resource Loading

- Explicitly specify relations to load with resources
- Use the `load()` method for eager loading related models
- Avoid N+1 query problems by using eager loading appropriately
- Load only necessary relationships to optimize performance

## Array Handling

- Use `Arr::except()` and similar helpers when manipulating request data
- Leverage dedicated methods on request classes for specialized data extraction
- Use array destructuring when appropriate

## Error Handling

- Let Laravel's exception handler manage errors when possible
- For custom error handling, use appropriate HTTP status codes
- Return detailed error messages in a consistent format

## Testing

- All controllers should have corresponding feature tests
- Test both successful operations and error cases
- Verify authorization, validation, and response structure
- Mock external services when testing controller functionality
- Always use DDEV for running tests to ensure a consistent environment across all development machines:

```shell
# Run all tests
ddev artisan test

# Run a specific test class
ddev artisan test --filter=EmployeeTest

# Run a specific test method
ddev artisan test --filter=EmployeeTest::test_method_name

# Run tests with specific filter and verbose output
ddev artisan test --filter=EmployeeTest --verbose
```

## Example Controller

```php
<?php

namespace App\Http\Controllers\Frontend;

use App\Http\Controllers\Controller;
use App\Http\Requests\Frontend\LocationStoreRequest;
use App\Http\Requests\Frontend\LocationUpdateRequest;
use App\Http\Resources\Frontend\LocationResource;
use App\Models\Location;
use App\QueryBuilders\LocationQueryBuilder;
use App\Services\CreateLocationService;
use App\Services\UpdateLocationService;
use App\Support\ApiResponse;
use Illuminate\Http\Request;

/**
 * @group Frontend
 * @subgroup Locations
 */
class LocationController extends Controller
{
    /**
     * Create a new controller instance.
     */
    public function __construct()
    {
        $this->authorizeResource(Location::class, "location");
    }

    /**
     * List locations
     *
     * Returns a paginated list of locations with filtering options.
     *
     * @apiResource App\Http\Resources\Frontend\LocationResource
     * @apiResourceModel App\Models\Location
     * @queryParam filter.search string Search by name or address.
     * @queryParam filter.is_active boolean Filter by active status.
     * @queryParam include string Include related resources. E.g. include=employees,devices
     * @queryParam per_page integer Number of items per page. Default is 15.
     */
    public function index(Request $request): ApiResponse
    {
        return new ApiResponse(
            data: LocationResource::collection(
                LocationQueryBuilder::query()
                    ->paginate($request->per_page ?? 15)
                    ->withQueryString()
            )
        );
    }

    /**
     * Create location
     *
     * Store a new location in the database.
     *
     * @apiResource App\Http\Resources\Frontend\LocationResource
     * @apiResourceModel App\Models\Location
     */
    public function store(LocationStoreRequest $request): ApiResponse
    {
        $location = CreateLocationService::handle($request->validated());

        return new ApiResponse(
            data: new LocationResource($location),
            message: __("The location has been created successfully.")
        );
    }

    /**
     * Show location
     *
     * Display detailed information about a specific location.
     *
     * @apiResource App\Http\Resources\Frontend\LocationResource
     * @apiResourceModel App\Models\Location with=employees,devices
     */
    public function show(Location $location, Request $request): ApiResponse
    {
        $location->load($request->input("include", ["employees", "devices"]));

        return new ApiResponse(data: new LocationResource($location));
    }

    /**
     * Update location
     *
     * Update an existing location with new information.
     *
     * @apiResource App\Http\Resources\Frontend\LocationResource
     * @apiResourceModel App\Models\Location
     */
    public function update(LocationUpdateRequest $request, Location $location): ApiResponse
    {
        UpdateLocationService::handle($location, $request->validated());

        return new ApiResponse(
            data: new LocationResource($location->fresh()),
            message: __("The location has been updated successfully.")
        );
    }

    /**
     * Delete location
     *
     * Remove a location from the system.
     */
    public function destroy(Location $location): ApiResponse
    {
        $location->delete();

        return new ApiResponse(message: __("The location has been deleted successfully."));
    }
}
```

# Nawart Attendance Backend Job Guidelines

## Purpose and Responsibility

- Jobs handle asynchronous or time-consuming operations that should be processed in the background
- Each job should have a single, well-defined responsibility
- Use jobs for operations that can be queued, retried, or scheduled
- Jobs should be idempotent (can be safely run multiple times with the same result)

## Naming Conventions

- Use descriptive names that clearly indicate the job's purpose
- End class names with `Job` suffix (e.g., `PrepareEmployeeAttendanceRecord`)
- Use verb-first naming that describes the action performed (e.g., `GenerateExcelPartJob`)
- Use clear method names that describe what the method does

## Structure

- Implement Laravel's `ShouldQueue` interface
- Use the `Queueable` trait (not the full set of Laravel job traits)
- Always include a properly typed constructor for dependencies
- Implement a `handle()` method that contains the main logic
- Use constructor property promotion for class properties
- Include appropriate visibility modifiers (public, private, protected)
- Use readonly properties when applicable

```php
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;

class ExampleJob implements ShouldQueue
{
    use Queueable;

    public function __construct(private readonly Employee $employee)
    {
    }

    public function handle(): void
    {
        // Job logic here
    }
}
```

## Queue Configuration

- Configure the appropriate queue and connection in the constructor when needed
- Consider using custom queues for specific types of jobs
- Set timeouts and retry configurations based on job complexity

```php
public function __construct(...)
{
    $this->onQueue('long-running-queue');
    $this->onConnection('redis-long-running');
}
```

## Input and Output

- **Prefer DTOs over arrays** for structured data input
- Always type-hint parameters in the constructor
- Make properties immutable (readonly) when they should not change
- Pass only the necessary data to jobs (avoid entire request objects)
- Store minimal data required to complete the job

## Error Handling

- Implement a `failed()` method for custom error handling
- Log errors appropriately using `report()`
- Update relevant models with failure status
- Consider using job batches for dependent jobs

```php
public function failed(?Throwable $exception): void
{
    $this->reportTask->markAsFailed($exception->getMessage());
    report($exception);
}
```

## Job Organization

- Use inheritance for job families with shared behavior
- Group related jobs in subdirectories if volume requires

## Performance Considerations

- Keep jobs focused and lightweight
- For resource-intensive tasks, consider chunking into smaller jobs
- Be mindful of memory usage
- Consider using unique jobs to prevent duplicates when appropriate

## Scheduling Jobs

- Use the scheduler for recurring jobs
- Set appropriate delays for jobs that should run after certain events
- Consider timezone implications for scheduled jobs

```php
SendEmployeeStatementIfApplicableJob::dispatch($attendance)->delay($shiftTo);
```

## Testing Jobs

- Write unit tests for job logic
- Test that jobs are dispatched correctly
- Verify job failure handling
- Use fake queue for testing dispatch

## Example Job

```php
<?php

namespace App\Jobs;

use App\DTOs\ReportData;
use App\Models\Employee;
use App\Models\ReportTask;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;
use Throwable;

class GenerateEmployeeReportJob implements ShouldQueue
{
    use Queueable;

    /**
     * The number of times the job may be attempted.
     */
    public $tries = 3;

    /**
     * The number of seconds the job can run before timing out.
     */
    public $timeout = 120;

    /**
     * Create a new job instance.
     */
    public function __construct(
        private readonly Employee $employee,
        private readonly ReportTask $reportTask,
        private readonly ReportData $reportData
    ) {
        $this->onQueue("reports");
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        // Generate the report using the provided data
        $path = $this->generateReport();

        // Update the report task with the result
        $this->reportTask->markAsCompleted($path);

        // Dispatch any follow-up jobs if needed
        NotifyReportCompletionJob::dispatch($this->employee, $path);
    }

    /**
     * Handle a job failure.
     */
    public function failed(?Throwable $exception): void
    {
        // Update the report task status
        $this->reportTask->markAsFailed($exception->getMessage());

        // Log the error
        report($exception);
    }

    /**
     * Generate the report file.
     */
    private function generateReport(): string
    {
        // Report generation logic
        return storage_path("reports/{$this->reportTask->id}.xlsx");
    }
}
```

## Jobs vs Other Patterns

- **Jobs vs Services**: Jobs run asynchronously; Services execute immediately
- **Jobs vs Events/Listeners**: Jobs are dispatched directly for specific tasks; Listeners respond to events
- **Jobs vs Commands**: Jobs are for background processing; Commands are for CLI operations
- **Jobs vs Notifications**: Jobs perform general work; Notifications specifically deliver messages

By following these guidelines, jobs will be well-structured, maintainable, and provide reliable background processing
throughout the application.

# Nawart Attendance Backend Model Guidelines

## Structure and Organization

- Models should represent database tables and follow Laravel Eloquent patterns
- Place all models in the `App\Models` namespace
- Use singular, PascalCase naming (e.g., `Employee`, not `Employees` or `employee`)
- Follow the principle of single responsibility
- Organize model code in a consistent order (properties, relationships, scopes, attributes, methods)

## Properties

- Define `$fillable` properties explicitly for mass assignment protection
- Include `$casts` for proper type handling
- Define `$webhookPayload` for models that are exposed via webhooks
- Use appropriate visibility modifiers (protected, public) for properties
- Consider using custom casts for complex attributes

```php
protected $fillable = [
    'team_id',
    'name',
    'lat',
    'lng',
    'radius',
    'timezone',
    'is_default',
];

protected $casts = [
    'id' => 'integer',
    'team_id' => 'integer',
    'lat' => 'double',
    'lng' => 'double',
    'is_default' => 'boolean',
];

public $webhookPayload = ['id', 'name', 'lat', 'lng', 'radius'];
```

## Traits and Interfaces

- Use the `HasFactory` trait for model factories
- Use `SoftDeletes` for models that require soft deletion
- Use `BelongsToTenant` for multi-tenant models
- Implement the `Auditable` interface for models that need audit logs
- Use `Notifiable` for models that receive notifications
- Apply appropriate traits based on model needs

```php
use Auditable;
use BelongsToTenant;
use HasFactory;
use Notifiable;
use SoftDeletes;
```

## Relationships

- Define relationship methods with clear, descriptive names
- Include return type declarations for IDE support and code clarity
- Group related relationships together
- Use appropriate relationship types (HasOne, HasMany, BelongsTo, etc.)

```php
public function employees(): MorphToMany
{
    return $this->morphedByMany(Employee::class, 'locationable')
        ->withPivot('id', 'permanent', 'start_date', 'end_date')
        ->withTimestamps();
}

public function devices(): HasMany
{
    return $this->hasMany(Device::class);
}
```

## Query Scopes

- Create query scopes for common filtering operations
- Prefix scope methods with `scope`
- Accept nullable parameters to make scopes optional
- Ensure scopes properly return the query builder

```php
public function scopeSearch(Builder $q, array|string|null $search = null): Builder
{
    if (blank($search)) {
        return $q;
    }

    $searchableColumns = ['first_name', 'last_name', 'email', 'number'];

    return $q->where(
        fn($q) => collect($search)
            ->filter()
            ->map('strtolower')
            ->each(fn($text) => $q->orWhereAny($searchableColumns, 'LIKE', "%$text%"))
    );
}

public function scopeAutomatic(Builder $query): Builder
{
    return $query->where('automatic', true);
}
```

## Accessors and Mutators

- Use Laravel's `Attribute` class for accessors and mutators
- Keep transformations simple and focused
- Add comments for complex transformations

```php
protected function email(): Attribute
{
    return Attribute::set(fn($value) => strtolower($value));
}
```

## Helper Methods

- Implement model-specific helper methods when needed
- Keep methods focused on a specific task
- Use clear, descriptive method names
- Add proper return type declarations

```php
public function isWithinLocation($lat, $lng, int $margin): bool
{
    $fullRadius = $this->radius + $margin;
    $distance = $this->calcDistance($lat, $lng, $this->lat, $this->lng);
    return $distance < $fullRadius;
}

public static function setAsDefaultLocation(Location $location): void
{
    $defaultLocation = Location::firstWhere('is_default', true);

    if ($defaultLocation && $defaultLocation->isNot($location)) {
        $defaultLocation->update(['is_default' => false]);
    }

    $location->update(['is_default' => true]);
}
```

## Static Methods

- Use static methods for operations that don't require a model instance
- Prefer instance methods when working with a specific model instance
- Use static methods for factory patterns or utility functions

```php
public static function clearCurrentDefaultLocation(): void
{
    Location::firstWhere('is_default', true)?->update(['is_default' => false]);
}
```

## Model Events

- Use model events when appropriate (created, updated, deleted, etc.)
- Consider using observers for complex event handling
- Keep event handlers focused and maintainable
- Document event handlers with clear comments

## Example Model

```php
<?php

namespace App\Models;

use App\Traits\BelongsToTenant;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use OwenIt\Auditing\Auditable;
use OwenIt\Auditing\Contracts\Auditable as AuditableContract;

class Department extends Model implements AuditableContract
{
    use Auditable;
    use BelongsToTenant;
    use HasFactory;
    use SoftDeletes;

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = ["team_id", "manager_id", "name", "description"];

    /**
     * The attributes that should be cast to native types.
     *
     * @var array
     */
    protected $casts = [
        "id" => "integer",
        "team_id" => "integer",
        "manager_id" => "integer",
    ];

    public $webhookPayload = ["id", "name", "description"];

    /**
     * Get the team that the department belongs to.
     */
    public function team(): BelongsTo
    {
        return $this->belongsTo(Team::class);
    }

    /**
     * Get the manager of the department.
     */
    public function manager(): BelongsTo
    {
        return $this->belongsTo(Employee::class, "manager_id");
    }

    /**
     * Get the employees in this department.
     */
    public function employees(): HasMany
    {
        return $this->hasMany(Employee::class);
    }

    /**
     * Scope a query to search departments by name.
     */
    public function scopeSearch(Builder $query, ?string $search): Builder
    {
        return $query->when(
            $search,
            fn(Builder $query) => $query->where("name", "like", "%{$search}%")
        );
    }

    /**
     * Check if the department has any employees.
     */
    public function hasEmployees(): bool
    {
        return $this->employees()->exists();
    }
}
```

By following these guidelines, models will be consistent, maintainable, and leverage Laravel's features effectively
throughout the application.

# Nawart Attendance Backend API Resource Guidelines

## Purpose and Responsibility

- API Resources transform models and other data into consistent JSON responses
- Resources handle the presentation layer, separating data formatting from business logic
- Each resource should provide a clean, consistent interface for a specific model or data type
- Resources should hide implementation details and only expose what's needed for clients

## Structure and Organization

- Place resources in the `App\Http\Resources` namespace
- Organize platform-specific resources in subdirectories:
    - `App\Http\Resources\Mobile`: Resources for mobile applications
    - `App\Http\Resources\Frontend`: Resources for web frontend
    - `App\Http\Resources\External`: Resources for external APIs
- Use versioned subdirectories when needed (e.g., `Mobile\V2`, `Mobile\V3`)
- All resources should extend Laravel's `JsonResource` base class

## Naming Conventions

- Use singular, PascalCase names with the `Resource` suffix
- Name resources after the model they represent (e.g., `EmployeeResource`, `LocationResource`)
- For specialized versions of a resource, add a descriptive prefix or suffix, or use a subdirectory

## Class Definition

- Add PHPDoc `@mixin` annotation to indicate which model the resource represents
- Keep resources focused on a single responsibility
- Implement the `toArray` method to define the resource structure

```php
<?php

namespace App\Http\Resources;

use App\Models\Tag;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/** @mixin Tag */
class TagResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        // Resource implementation
    }
}
```

## Response Structure

- Use consistent naming conventions for attributes
- Include all necessary fields but avoid exposing unnecessary data
- Use camelCase for JSON attributes when returning them to clients
- Ensure nested resources follow the same formatting patterns

```php
public function toArray($request): array
{
    return [
        'id' => $this->id,
        'name' => $this->name,
        'color' => $this->color,
    ];
}
```

## Loading Relationships

- Use the `whenLoaded` method to include relationships only when they're loaded
- Use nested resources for related models
- Consider performance implications when loading relationships

```php
'department' => DepartmentResource::make($this->whenLoaded('department')),
'shifts' => ShiftResource::collection($this->whenLoaded('shifts')),
```

## Conditional Attributes

- Use the `when` and `whenHas` methods for conditional attributes
- Use the `whenPivotLoaded` method for pivot table attributes
- Provide sensible default values for conditional attributes

```php
'employees_count' => $this->whenHas('total_employees'),
'assignment_id' => $this->whenPivotLoaded('locationables', fn() => $this->pivot->id),
```

## Customization Options

- Add methods to customize resource behavior when needed
- Return `$this` from customization methods to allow method chaining
- Document customization options clearly

```php
protected bool $useNawartUUidAsId = false;

public function useNawartUUidAsId(): static
{
    $this->useNawartUUidAsId = true;
    return $this;
}
```

## Handling Configuration Values

- Use configuration lookups to translate enum-like values to human-readable formats
- Provide both values and human-readable descriptions when appropriate
- Use proper translation functions for localized text

```php
$remoteWork = Arr::first(
    config("lookups.remote_work_policy"),
    fn($p) => $p["value"] === $this->whenHas("remote_work")
);

return [
    // ...
    "remote_work" => $remoteWork
        ? [
            "title" => __($remoteWork["title"]),
            "description" => __($remoteWork["description"]),
            "value" => $remoteWork["value"],
        ]
        : null,
    // ...
];
```

## Resource Collections

- Use `collection` method for returning multiple instances of a resource
- Consider creating custom resource collections when additional metadata is needed
- Be consistent with pagination formats

```php
'attendances' => AttendanceResource::collection($this->whenLoaded('attendances')),
```

## Versioning Strategy

- Create versioned resources in appropriate subdirectories
- Maintain backward compatibility when possible
- Clearly mark deprecated fields

```php
'random_notification' => $randomNotification // deprecated
    ? [
        'title' => __($randomNotification['title']),
        'description' => __($randomNotification['description']),
        'value' => $randomNotification['value'],
    ]
    : null,
```

## Complete Example

```php
<?php

namespace App\Http\Resources;

use App\Models\Location;
use Illuminate\Http\Resources\Json\JsonResource;

/** @mixin Location */
class LocationResource extends JsonResource
{
    public function toArray($request): array
    {
        return [
            "id" => $this->id,
            "name" => $this->name,
            "lat" => $this->lat,
            "lng" => $this->lng,
            "radius" => $this->radius,
            "timezone" => $this->timezone,
            "is_default" => $this->is_default,
            "automatic" => $this->automatic,
            "check_out_radius" => $this->check_out_radius,

            // Conditional attributes
            "employees_count" => $this->whenHas("total_employees"),
            "present_count" => $this->whenHas("present_employees"),

            // Pivot data
            "assignment_id" => $this->whenPivotLoaded("locationables", fn() => $this->pivot->id),
            "permanent" => $this->whenPivotLoaded("locationables", fn() => $this->pivot->permanent),
            "start_date" => $this->whenPivotLoaded(
                "locationables",
                fn() => $this->pivot->start_date
            ),
            "end_date" => $this->whenPivotLoaded("locationables", fn() => $this->pivot->end_date),
        ];
    }
}
```

By following these guidelines, API resources will provide consistent, well-structured responses across all endpoints in
the application while maintaining separation between data presentation and business logic.

# Nawart Attendance Backend Service Guidelines

## Purpose and Responsibility

- Services encapsulate business logic and complex operations
- Each service should have a single, well-defined responsibility
- Move business logic out of controllers and into dedicated service classes
- Services should be reusable across different parts of the application

## Naming Conventions

- Use descriptive names that clearly indicate the service's purpose
- End class names with `Service` suffix (e.g., `UpdateEmployeeService`)
- Use verb-first naming for action-oriented services (e.g., `ApproveLeaveService`)
- Use clear method names that describe what the method does, not how it does it

## Method Structure

- Implement a primary method named `handle()` that executes the service's main functionality
- For services with static methods, use `handle()` as a static method
- For services with instance methods, use `handle()` as an instance method
- Keep methods focused on a single task
- Use descriptive parameter names

## Input and Output

- **ALWAYS prefer DTOs over arrays** for structured data input and output
- Create dedicated DTO classes for complex data structures
- Use type hints to ensure data integrity and improve code completion
- DTOs provide better documentation, validation, and maintainability than arrays
- When accepting arrays, consider converting them to DTOs at the earliest opportunity
- Return typed objects (models, collections, DTOs) rather than arrays when possible
- Document expected inputs and outputs
- Validate inputs at the beginning of the method when necessary

## Service Organization

- Create separate service classes for distinct business operations
- Group related service classes in subdirectories if volume requires
- Keep services flat and avoid deep inheritance hierarchies
- Prefer composition over inheritance when sharing functionality

## Transactions

- Use database transactions for operations that modify multiple records
- Wrap related database operations in a single transaction
- Handle transaction errors appropriately

```php
DB::transaction(function () use ($data, $employee) {
    // Database operations
});
```

## Event Dispatching

- Dispatch events after successful operations when appropriate
- Use events to trigger side effects and maintain loose coupling
- Name events clearly to describe what has happened (past tense)

```php
event(new EmployeeUpdated($employee));
```

## Notification Handling

- Services may dispatch notifications after successful operations
- Keep notification logic organized and focused
- Consider separating complex notification logic into dedicated methods

## Error Handling

- Use appropriate exception handling
- Throw specific exceptions that clearly indicate the error
- Document potential exceptions that may be thrown
- Catch and handle exceptions at appropriate levels

## Dependency Management

- Use constructor injection for required dependencies
- Avoid accessing global state directly
- Use method injection for dependencies only needed by specific methods
- Keep the number of dependencies manageable

## Example Service

```php
<?php

namespace App\Services;

use App\DTOs\LocationData;
use App\Events\LocationCreated;
use App\Models\Location;
use App\Models\Team;
use DB;

class CreateLocationService
{
    public static function handle(LocationData $data): Location
    {
        return DB::transaction(function () use ($data) {
            // Reset default location if new one is set as default
            if ($data->isDefault) {
                Team::find($data->teamId)
                    ->locations()
                    ->where("is_default", true)
                    ->update(["is_default" => false]);
            }

            // Create the new location
            $location = Location::create($data->toArray());

            // Dispatch event
            event(new LocationCreated($location));

            return $location;
        });
    }
}
```

## Testing

- Write unit tests for each service
- Test all significant code paths and edge cases
- Verify expected side effects (events, notifications, database changes)
- Test both success and failure scenarios

## Service vs Other Patterns

- **Services vs Jobs**: Services execute immediately; Jobs are queued
- **Services vs Listeners**: Services are called directly; Listeners respond to events
- **Services vs Actions**: Services handle complex business logic; Actions are smaller, single-purpose operations
- **Services vs Controllers**: Services contain business logic; Controllers handle HTTP requests

By following these guidelines, services will remain focused, maintainable, and provide a clear separation of concerns
throughout the application.

# Nawart Attendance Backend Test Guidelines

## Structure and Organization

- Tests should be organized into two main directories: `Feature` and `Unit`
- Feature tests verify functionality from a user's perspective and typically make HTTP requests
- Unit tests verify isolated components like methods and classes
- Create subdirectories that mirror the application's structure (Controllers, Services, Jobs, etc.)
- Use descriptive naming for test files with suffix `Test.php` (e.g., `LocationControllerTest.php`)
- Group related tests together and structure test files to match the controller/service structure

## Pest Testing Framework

- Use Pest PHP's expressive syntax for all tests (`it`, `test`, `expect`, etc.)
- Follow the function-based testing style rather than class-based PHPUnit style
- Leverage global helper functions from Pest Laravel (`assertDatabaseHas`, `travelTo`, etc.)
- Take advantage of Pest's fluent assertion API with `expect()`
- **Ensure each test is independent** - avoid using `foreach` loops to generate tests as this can make debugging
  difficult

## Database Operations

- Create test data using factories with precise state definitions
- Clean up after tests to ensure they are isolated from each other
- Use specific database assertions like `assertDatabaseHas`, `assertDatabaseMissing`
- Employ `freezeTime()` and `travelTo()/travelBack()` for time-dependent tests

## Authentication and Authorization

- Use the custom JWT authentication helpers (`jwtActingAs...`) for different user roles
- Test authorization failures by attempting operations with unauthorized users
- Verify proper authentication mechanisms are in place for protected routes
- Test resource-based authorization using policy rules

## HTTP Testing

- Use HTTP testing methods (`getJson`, `postJson`, `putJson`, `deleteJson`) for API tests
- Verify proper status codes (`assertOk`, `assertCreated`, etc.)
- Check response content with `assertJson` or by accessing response properties
- Test validation errors by sending incorrect or incomplete data
- Include tests for edge cases and error scenarios (404, 403, etc.)

## Event Testing

- Use `Event::fake()` to verify events are dispatched
- Include assertions for event payloads with `Event::assertDispatched`
- Test event listeners in isolation when necessary
- Verify event handling through observable behavior

## Test Simplicity and Realism

- **Keep tests simple and direct** - avoid over-engineering test scenarios
- **Prefer real implementations over mocks** - use the actual system components when possible
- Only mock external services or APIs that would cause unpredictable or slow tests
- **Focus on testing business logic** - prioritize testing the core business rules and requirements
- Focus on testing actual behavior rather than implementation details
- Use real database interactions instead of complicated mocks when feasible

## Assertions and Expectations

- Use precise assertions that verify the exact behavior expected
- Prefer `expect()` syntax for more readable tests
- Include assertions for both positive and negative cases
- Test both successful operations and error scenarios
- Verify response structures match API contracts

## Test Data and Factories

- Leverage model factories to create test data efficiently
- Define reusable factory states for common scenarios
- Use factories with relationship chaining for complex object graphs
- Define test datasets for repeatable and consistent test data

## Testing Commands and Jobs

- Test Artisan commands by running them with `$this->artisan()`
- Verify command output and behavior with assertions
- Test jobs by dispatching and asserting their effects
- Prefer testing with real dependencies when possible

## Example Test Case

```php
<?php

use App\Events\LocationAdded;
use App\Models\Employee;
use App\Models\Location;
use function Pest\Laravel\assertDatabaseHas;

test("create location successfully", function () {
    // Arrange: Set up test data and dependencies
    Event::fake([LocationAdded::class]);

    $employee = Employee::factory()
        ->for($this->tenant)
        ->create();

    $defaultLocation = Location::factory()
        ->for($this->tenant)
        ->create(["is_default" => true]);

    $data = [
        "name" => "location A",
        "lat" => 24.55,
        "lng" => 46.33,
        "radius" => 30,
        "timezone" => fake()->timezone(),
        "is_default" => true,
        "automatic" => false,
        "check_out_radius" => 0,
    ];

    // Act: Perform the action being tested
    $response = $this->jwtActingAsAttendanceHR($employee)->postJson(
        "api/v1/frontend/locations",
        $data
    );

    // Assert: Verify the expected outcomes
    $response->assertOk();

    assertDatabaseHas("locations", [...$data, "team_id" => $this->tenant->id]);

    expect($defaultLocation->refresh()->is_default)->toBeFalse();

    Event::assertDispatched(
        LocationAdded::class,
        fn($event) => $event->location->is(Location::firstWhere("name", "location A"))
    );
});

test("update location validation errors", function () {
    $employee = Employee::factory()
        ->for($this->tenant)
        ->create();

    $location = Location::factory()
        ->for($this->tenant)
        ->create();

    $this->jwtActingAsAttendanceHR($employee)
        ->putJson("api/v1/frontend/locations/{$location->id}", [
            // Missing required fields
        ])
        ->assertStatus(422)
        ->assertJsonValidationErrors(["name", "lat", "lng", "radius"]);
});
```

This example demonstrates a proper test structure with clear arrange-act-assert sections, proper assertions, and testing
of both successful operations and validation errors.

# Nawart Attendance Backend Tooling Guidelines

This document provides an overview of the tools and development workflow used in the Nawart Attendance Backend project.

## Local Development Environment

### DDEV

We use [DDEV](https://ddev.com) as our local development environment:

```shell
# Start the DDEV project
ddev start

# Stop the DDEV project
ddev stop

# Install dependencies
ddev composer install

# Run migrations
ddev artisan migrate

# Run the application
ddev launch
```

## Laravel Artisan Commands

### Database Commands

```shell
# Run migrations
ddev artisan migrate

# Rollback migrations
ddev artisan migrate:rollback

# Fresh migrations with seed
ddev artisan migrate:fresh --seed

# Generate the database schema file
ddev artisan schema:dump
```

### Model Exploration

```shell
# Show model details (fields, relationships, etc.)
ddev artisan model:show Employee

# List all models
ddev artisan model:list
```

### Route Commands

```shell
# List all routes
ddev artisan route:list

# List routes with specific filter
ddev artisan route:list --path=employees
```
