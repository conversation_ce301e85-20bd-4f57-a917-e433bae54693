# Nawart SSO & Subscription management system

The system responsible for authenticating the employees of Nawart 360 products, The authentication of the products is
done throw OAuth,
We are using [Laravel Passport](https://laravel.com/docs/10.x/passport) For OAuth.

## Installation and configuration

Add `auth.json` file to the root of the project with the following content:

#### Note: you have to ask a team member for the password or get it from ci/cd variables in gitlab under name NOVA_TOKEN

```json
{
    "http-basic": {
        "nova.laravel.com": {
            "username": "<EMAIL>",
            "password": ""
        }
    }
}
```

### Configuration:

-   First you have to create the config file

```sh
cp .env.example .env
```

-   Generate Laravel Passport keys

```sh
php artisan passport:keys
```

### SAML Configuration

-   Optional (only if you are using saml): Configure sp certificate and private key

You have to create a key pair using openssl then you have to

```sh
openssl req -new -x509 -days 365 -nodes -sha256 -out saml.crt -keyout saml.pem
```

Then

-   copy the contacts of saml.crt and put it into the env `SAML_SP_CERT_x509`
-   copy the contacts of saml.pem and put it into the env `SAML_SP_PRIVATEKEY`

your env should look like:

```env
SAML_SP_CERT_x509="-----BEGIN CERTIFICATE-----
MIIDazCCAlOgAwIBAgIUOmtrGXfd7hRYO7AuMgU3Gse1OrMwDQYJKoZIhvcNAQEL
BQAwRTELMAkGA1UEBhMCQT6AzlAWGGo96r8OfjLy43I5TxIMZpen37czJE+fAD7
eTk/Wg7rx64lwxb6VB1U...
-----END CERTIFICATE-----"
SAML_SP_PRIVATEKEY="-----BEGIN PRIVATE KEY-----
MIIEvgIBADANBgkqhkiG9w0BAQEFAASCBKgwggSkAgEAAoIBAQDGpeq3CJuwUOhn
+oexc+HE5ifgGIQj3cjdxJIYr0iCnd3O75lXurdm1fmh7c1GeY/p+4NkF6V1qGzG
/WzTWXC/QxUJzZ/UsZn.....
-----END PRIVATE KEY-----"
```

### Running the application

The application was configured to run using [DDEV](https://ddev.readthedocs.io/en/stable/) so please head to DDEV
documentation and install DDEV.

```sh
ddev start
ddev composer install
ddev php artisan migrate
```

Then you can run describe to get information about the development environment

```sh
ddev describe
```

### Manually Testing the API locally (Frontend APIs)

We are using Laravel Passport for OAuth, so typically users have to authenticate using the UI,
but for development purposes, you can create a token manually and use it to authenticate the API.

```shell
ddev php artisan passport:client --personal
```

then for the employee that you want to test

```php
$employee->createToken("test")->accessToken;
```

then you can hit the API in any tool

```http request
GET https://nawart-portal-backend.ddev.site/api/v1/tenant/employees
Accept: application/json
Content-Type: application/json
Authorization: Bearer <access-token> ----------------------> here
```

then you can hit any API in other Nawart Modules, here we are hitting an API in Nawart Drive:

```http request
GET https://nawart-drive-backend.ddev.site/api
Accept: application/json
Content-Type: application/json
Authorization: Bearer <access-token> ----------------------> here
```

**Important** this works on APIs that have `auth:jwt` middleware

### Manually Testing the API locally (External APIs)

We are using Laravel Sanctum for external APIs, so you can easily create a token for the tenant

```php
$tenant->createToken("test")->plainTextToken;
```

then you can hit the API in any tool

```http request
GET https://nawart-portal-backend.ddev.site/api/v1/external/employees
Accept: application/json
Content-Type: application/json
Authorization: Bearer <access-token> ----------------------> here
```

**Important** since this is a sanctum API you can't use the token in other Nawart Modules.
