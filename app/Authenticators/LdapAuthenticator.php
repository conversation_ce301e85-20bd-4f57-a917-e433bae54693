<?php

namespace App\Authenticators;

use App\DTOs\Identifier;
use App\Interfaces\Authenticators\BaseAuthenticatorInterface;
use App\Interfaces\Authenticators\ExternalAuthenticatorInterface;
use App\Interfaces\Authenticators\PasswordAuthenticatorInterface;
use App\Models\Employee;
use App\Models\Tenant;
use App\Models\TenantAuthenticatorConfig;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
use LdapRecord\Connection;

/**
 * @deprecated please do not use ldap for authentcation it's better to use SAML, Oauth or OIDC
 * @codeCoverageIgnore
 */
class LdapAuthenticator implements
    BaseAuthenticatorInterface,
    ExternalAuthenticatorInterface,
    PasswordAuthenticatorInterface
{
    private Connection $connection;

    private $usernameField; // to search for a username in ldap

    private $distinguishednameField = 'distinguishedname';

    private $firstNameField = 'cn';

    private $lastNameField = 'sn';

    private $employeeNumberField = null;

    private $errors = [];

    public function configure(TenantAuthenticatorConfig $config): void
    {
        Log::info('Starting Ldap Connection');
        $this->connection = new Connection([
            'hosts' => [$config->ldapHost],
            'port' => $config->ldapPort,
            'base_dn' => $config->ldapBaseDN,
            'username' => $config->ldapUsername,
            'password' => $config->ldapPassword,
            'use_ssl' => $config->ldapUseSSL,
            'use_tls' => $config->ldapUseTLS,
            'use_sasl' => $config->ldapUseSASL,
        ]);

        $this->usernameField = $config->ldapUsernameField;
        $this->distinguishednameField = $config->ldapDistinguishednameField;
        $this->firstNameField = $config->ldapFirstNameField;
        $this->lastNameField = $config->ldapLastNameField;
        $this->employeeNumberField = $config->ldapEmployeeNumberField;

        try {
            $this->connection->connect();

            Log::info('Ldap Connected Successful For Tenant: ' . $config->tenant_id);
        } catch (\LdapRecord\Auth\BindException $e) {
            $error = $e->getDetailedError();

            Log::error('Could not connect to ldap with Error Code ' . $error->getErrorCode());
            Log::error('Error Message ' . $error->getErrorMessage());
            Log::error('Diagnostic Message ' . $error->getDiagnosticMessage());
        }

        Log::info('End Ldap Connection');
    }

    public function authenticate(
        Identifier $identifier,
        Tenant $tenant,
        ?string $password = null
    ): bool {
        $ldapUser = $this->connection
            ->query()
            ->where($this->usernameField, '=', explode('@', $identifier->value())[0])
            ->first();

        if (!$ldapUser) {
            $this->errors[] = __(
                'User where not found in the Ldap of your company, please contact your administrator.'
            );

            return false;
        }

        if (!Arr::has($ldapUser, $this->distinguishednameField)) {
            $this->errors[] = __(
                'Ldap misconfigured in your company, please contact your administrator.'
            );

            return false;
        }

        $dn = $ldapUser[$this->distinguishednameField];
        if (!$this->connection->auth()->attempt($dn, $password)) {
            $this->errors[] = trans('auth.failed');

            return false;
        }

        // TODO: update the employee name form ldap
        $employee = Employee::where('tenant_id', $tenant->id)
            ->where('email', $identifier->value())
            ->first();

        $employeeNumber = Str::random();
        if ($this->employeeNumberField) {
            $employeeNumber = @$ldapUser[$this->employeeNumberField][0];
        }

        $attriubtes = [
            'tenant_id' => $tenant->id,
            'email' => $identifier->value(),
            'first_name' => @$ldapUser[$this->firstNameField][0],
            'last_name' => @$ldapUser[$this->lastNameField][0],
            'password' => Str::random(40),
            'position' => '--',
            'number' => $employeeNumber,
        ];

        if (!$employee) {
            $employee = Employee::create($attriubtes);
        } else {
            $employee->update(Arr::except($attriubtes, ['tenant_id', 'email', 'password']));
        }

        auth()->login($employee);

        return true;
    }

    public function errors(): array
    {
        return $this->errors;
    }
}
