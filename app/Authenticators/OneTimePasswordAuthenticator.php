<?php

namespace App\Authenticators;

use App\DTOs\Identifier;
use App\Interfaces\Authenticators\BaseAuthenticatorInterface;
use App\Interfaces\Authenticators\PasswordlessAuthenticatorInterface;
use App\Models\Otp;
use App\Models\Tenant;
use App\Models\TenantAuthenticatorConfig;

class OneTimePasswordAuthenticator implements
    BaseAuthenticatorInterface,
    PasswordlessAuthenticatorInterface
{
    private array $errors = [];

    public function configure(TenantAuthenticatorConfig $config): void
    {
    }

    public function authenticate(
        Identifier $identifier,
        Tenant $tenant,
        ?string $password = null
    ): bool {
        $employee = $tenant->employees()->identifier($identifier)->firstOrFail();

        if ($identifier->isTestIdentifier()) {
            auth()->login($employee);

            return true;
        }

        $otp = Otp::get($employee, $identifier);

        if (!$otp || $otp->isExpired()) {
            $this->errors[] = __('Your session is expired, we sent a new code, please try again.');
            $this->resendOtp($tenant, $identifier);

            return false;
        }

        if ($otp->validate($password)) {
            auth()->login($employee);

            return true;
        }

        $this->errors[] = __('One time password is wrong, please try again.');

        return false;
    }

    public function errors(): array
    {
        return $this->errors;
    }

    public function sendOtp(Tenant $tenant, Identifier $identifier): bool
    {
        if ($identifier->isTestIdentifier()) {
            return true;
        }

        Otp::getOrGenerate(
            employee: $tenant->employees()->identifier($identifier)->firstOrFail(),
            identifier: $identifier
        )->send($identifier);

        return true;
    }

    public function resendOtp(Tenant $tenant, Identifier $identifier): bool
    {
        if ($identifier->isTestIdentifier()) {
            return true;
        }

        Otp::getOrGenerate(
            employee: $tenant->employees()->identifier($identifier)->firstOrFail(),
            identifier: $identifier
        )->send($identifier);

        return true;
    }

    public function invalidateOtp(Tenant $tenant, Identifier $identifier): void
    {
        Otp::invalidate($tenant, $identifier);
    }
}
