<?php

namespace App\Authenticators;

use App\DTOs\Identifier;
use App\Interfaces\Authenticators\BaseAuthenticatorInterface;
use App\Interfaces\Authenticators\ExternalAuthenticatorInterface;
use App\Interfaces\Authenticators\RedirectAuthenticatorInterface;
use App\Models\Tenant;
use App\Models\TenantAuthenticatorConfig;
use App\Services\Saml\SamlAuthnRequestService;

/**
 * @codeCoverageIgnore until we are using it
 */
class SamlAuthenticator implements
    BaseAuthenticatorInterface,
    ExternalAuthenticatorInterface,
    RedirectAuthenticatorInterface
{
    private TenantAuthenticatorConfig $config;

    private string $redirectTo;

    public function configure(TenantAuthenticatorConfig $config): void
    {
        $this->config = $config;
    }

    public function authenticate(
        Identifier $identifier,
        Tenant $tenant,
        ?string $password = null
    ): bool {
        $this->redirectTo = SamlAuthnRequestService::make($this->config, 'get', true); // this will redirect user to authenticator

        return true;
    }

    public function getRedirectTo()
    {
        return $this->redirectTo;
    }

    public function errors(): array
    {
        return [];
    }
}
