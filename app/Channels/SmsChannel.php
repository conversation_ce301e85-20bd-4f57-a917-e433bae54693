<?php

namespace App\Channels;

use App\DTOs\Identifier;
use App\Models\Employee;
use App\Models\Otp;
use App\Services\SmsService;
use Illuminate\Notifications\Notification;

class SmsChannel
{
    public function send(Employee $notifiable, Notification $notification): void
    {
        /** @var Otp $otp */
        [
            'otp' => $otp,
            'message' => $message,
        ] = $notification->toSms();

        SmsService::send(identifier: new Identifier($notifiable->phone), message: $message);
    }
}
