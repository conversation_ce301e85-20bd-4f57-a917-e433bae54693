<?php

namespace App\Claims;

use App\Models\Employee;
use CorBosman\Passport\AccessToken;

class EmployeeAndRolesClaim
{
    /**
     * @codeCoverageIgnore
     */
    public function handle(AccessToken $token, $next)
    {
        /**
         * @var Employee
         */
        $employee = Employee::find($token->getUserIdentifier());

        $token->addClaim('employee', [
            // todo: tenant_id is not needed here, we will remove it after frontend is updated
            'tenant_id' => $employee->tenant_id,
            'id' => $employee->id,
            'name' => $employee->name,
            'email' => $employee->email,
            'preferred_language' => $employee->preferred_language,
        ]);

        $token->addClaim('tenant', [
            'id' => $employee->tenant->id,
            'name' => $employee->tenant->name,
            'color' => $employee->tenant->color,
        ]);

        $token->addClaim('roles', $employee->roles()->pluck('name'));

        return $next($token);
    }
}
