<?php

namespace App\Console\Commands;

use App\Models\Employee;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class SaipMigrationCommand extends Command
{
    /**`
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:migration-saip-command';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'this command will run one time for migration from old visitors database to new database';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        DB::transaction(function () {
            $pgsql = DB::build([
                'driver' => 'pgsql',
                'host' => getenv('DB_HOST_OLD_APPOINTMENTS'),
                'port' => getenv('DB_PORT_OLD_APPOINTMENTS'),
                'database' => getenv('DB_DATABASE_OLD_APPOINTMENTS'),
                'username' => getenv('DB_USERNAME_OLD_APPOINTMENTS'),
                'password' => getenv('DB_PASSWORD_OLD_APPOINTMENTS'),
            ]);

            $employees = $pgsql->table('owners')->get();

            foreach ($employees as $employee) {
                Employee::firstOrCreate([
                    'tenant_id' => app()->environment('production')
                        ? '9e9e0985-f1ed-46c5-bacc-9cf39895111f'
                        : '9e5def03-2dd4-417f-9def-bbe01393dfa8',

                    'email' => $employee->email,
                    'first_name' => $employee->name,
                ]);
            }
        });
    }
}
