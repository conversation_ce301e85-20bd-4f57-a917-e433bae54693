<?php

namespace App\Console\Commands;

use App\Models\Software;
use App\Models\Subscription;
use App\Models\Tag;
use App\Models\Tenant;
use App\Observers\DepartmentObserver;
use App\Observers\EmployeeObserver;
use App\Observers\SoftwareObserver;
use App\Observers\SubscriptionItemObserver;
use App\Observers\TagObserver;
use App\Observers\TenantObserver;
use Illuminate\Console\Command;

class TriggerSyncAllCommand extends Command
{
    protected $signature = 'sync:all --force';

    protected $description = 'Command description';

    public function handle(): void
    {
        if (app()->environment('production') && !$this->option('force')) {
            $this->error('This command is not allowed in production! Use --force to override.');
            return;
        }

        $this->info('Triggering sync for all tenants...');

        Tenant::each(function ($tenant) {
            (new TenantObserver())->updated($tenant);

            $tenant->departments()->each(function ($department) {
                (new DepartmentObserver())->updated($department);

                $department->employees()->each(function ($employee) {
                    (new EmployeeObserver())->updated($employee);
                });
            });
        });

        $this->info('Triggering sync for all softwares...');

        Software::each(function ($software) {
            (new SoftwareObserver())->updated($software);
        });

        $this->info('Triggering sync for all subscriptions...');

        Subscription::each(function (Subscription $subscription) {
            $subscription->subscriptionItems()->each(function ($subscriptionItem) {
                (new SubscriptionItemObserver())->updated($subscriptionItem);
            });
        });

        $this->info('Triggering sync for all tags...');
        Tag::each(function ($tag) {
            (new TagObserver())->updated($tag);
        });
    }
}
