<?php

namespace App\Console;

use Illuminate\Console\Scheduling\Schedule;
use Illuminate\Foundation\Console\Kernel as ConsoleKernel;

class Kernel extends ConsoleKernel
{
    /**
     * @codeCoverageIgnore
     * Define the application's command schedule.
     */
    protected function schedule(Schedule $schedule): void
    {
        $schedule
            ->command('audit:prune')
            ->daily()
            ->withoutOverlapping()
            ->when(config('audit.enabled'));

        $schedule->command('passport:purge')->hourly();

        $schedule->command('sanctum:prune-expired --hours=24')->daily();

        $schedule->command('app:token-expiration-reminder')->weekly();

        $schedule->command('clear:temp-files')->daily();

        $schedule->command('refresh:tenant-logos')->daily();
    }

    /**
     * Register the commands for the application.
     */
    protected function commands(): void
    {
        $this->load(__DIR__ . '/Commands');

        require base_path('routes/console.php');
    }
}
