<?php

namespace App\Enums;

use App\Channels\SmsChannel;

enum IdentifierType: string
{
    case Email = 'email';
    case Phone = 'phone';

    public function channel(): string
    {
        return match ($this) {
            self::Email => 'mail',
            self::Phone => SmsChannel::class,
        };
    }

    public function isPhone(): bool
    {
        return $this->value === self::Phone->value;
    }

    public function isEmail(): bool
    {
        return $this->value === self::Email->value;
    }
}
