<?php

namespace App\Enums;

enum SoftwareCode: string
{
    case Start = 'start';
    case Attendance = 'attendance';
    case Visitors = 'visitors';
    case Drive = 'drive';

    public function name(): string
    {
        return match ($this->value) {
            self::Start->value => 'Start',
            self::Attendance->value => 'Attendance',
            self::Visitors->value => 'Visitors',
            self::Drive->value => 'Drive',
        };
    }

    public function translated()
    {
        return __($this->value);
    }
}
