<?php

namespace App\Enums;

enum SyncName: string
{
    case TenantCreate = 'tenant.create';
    case TenantUpdate = 'tenant.update';

    case EmployeeCreate = 'tenant.employee.create';
    case EmployeeUpdate = 'tenant.employee.update';

    case DepartmentCreate = 'tenant.department.create';
    case DepartmentUpdate = 'tenant.department.update';
    case DepartmentDelete = 'tenant.department.delete';

    case SubscriptionCreate = 'tenant.subscription.create';
    case SubscriptionUpdate = 'tenant.subscription.update';
    case SubscriptionDelete = 'tenant.subscription.delete';

    case SubscriptionItemCreate = 'tenant.subscription-item.create';
    case SubscriptionItemUpdate = 'tenant.subscription-item.update';
    case SubscriptionItemDelete = 'tenant.subscription-item.delete';

    case SoftwareCreate = 'tenant.software.create';
    case SoftwareUpdate = 'tenant.software.update';
    case SoftwareDelete = 'tenant.software.delete';

    case TagCreate = 'tenant.tag.create';
    case TagUpdate = 'tenant.tag.update';
    case TagDelete = 'tenant.tag.delete';
}
