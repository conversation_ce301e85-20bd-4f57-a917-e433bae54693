<?php

namespace App\Http\Controllers\API\V1;

use App\Http\Controllers\Controller;
use App\Support\ApiResponse;

/**
 * @group Frontend
 * @subgroup Employees
 */
class ActivateEmployeeController extends Controller
{
    /**
     * Activate
     */
    public function __invoke($employeeId)
    {
        $employee = currentTenant()->employees()->where('id', $employeeId)->firstOrFail();

        $employee->update(['is_active' => true]);

        return new ApiResponse(message: __('employee activated successfully'));
    }
}
