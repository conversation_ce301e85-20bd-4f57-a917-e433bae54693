<?php

namespace App\Http\Controllers\API\V1;

use App\Http\Controllers\Controller;
use App\Http\Requests\BulkAttachTagToEmployeesRequest;
use App\QueryBuilders\EmployeeQueryBuilder;
use App\Services\SyncEmployeeTagsService;
use App\Support\ApiResponse;

/**
 * @group Frontend
 * @subgroup Employees
 */
class AttachTagsToEmployeesController extends Controller
{
    /**
     * Attach Tags
     *
     * You either send `employees` as employee ids or `all` with `filters`
     */
    public function __invoke(BulkAttachTagToEmployeesRequest $request): ApiResponse
    {
        $data = $request->validated();

        $request->validate([
            'filter.search' => ['nullable', 'string'],
            'filter.employees' => ['nullable', 'array'],
            'filter.employees.*' => ['string'],
            'filter.departments' => ['nullable', 'array'],
            'filter.departments.*' => ['string'],
            'filter.tags' => ['nullable', 'array'],
            'filter.tags.*' => ['integer'],
            'filter.exclude' => ['nullable'],
            'filter.is_active' => ['nullable'],
        ]);

        $employees =
            $data['all'] ?? false
                ? EmployeeQueryBuilder::query()
                : currentTenant()
                    ->employees()
                    ->whereIn('id', $data['employees']);

        $employees->each(function ($employee) use ($data) {
            return (new SyncEmployeeTagsService($employee, $data['tags']))->handle();
        });

        return new ApiResponse(message: __('tags was added to employees successfully'));
    }
}
