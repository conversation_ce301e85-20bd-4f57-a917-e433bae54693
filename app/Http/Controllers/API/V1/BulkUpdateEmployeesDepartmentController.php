<?php

namespace App\Http\Controllers\API\V1;

use App\Http\Controllers\Controller;
use App\Http\Requests\BulkUpdateEmployeesDepartmentRequest;
use App\Models\Department;
use App\Models\Employee;
use App\QueryBuilders\EmployeeQueryBuilder;
use App\Support\ApiResponse;

class BulkUpdateEmployeesDepartmentController extends Controller
{
    /**
     * Bulk update employees department
     * @apiResource App\Http\Resources\EmployeeResource
     * @apiResourceModel App\Models\Employee
     * @queryParam filter.search string Search by name, email, or phone number.
     * @queryParam filter.ids array Filter by employee ids.
     * @queryParam filter.departments array Filter by department ids.
     * @queryParam filter.locations array Filter by location ids.
     * @queryParam filter.shifts array Filter by shift ids.
     * @queryParam filter.tags array Filter by tag ids.
     * @queryParam filter.exclude integer Exclude employee by id.
     * @queryParam filter.is_active boolean Filter by active status.
     */
    public function __invoke(BulkUpdateEmployeesDepartmentRequest $request, Department $department)
    {
        $employeeQuery = $request->boolean('all')
            ? EmployeeQueryBuilder::query()
            : currentTenant()
                ->employees()
                ->whereIn('id', $request->employees);

        $employeeQuery->eachById(function (Employee $employee) use ($department) {
            $employee->update(['department_id' => $department->id]);
        });

        return new ApiResponse(message: __('Employees department has been updated successfully'));
    }
}
