<?php

namespace App\Http\Controllers\API\V1;

use App\Http\Controllers\Controller;
use App\Http\Resources\TenantResource;
use App\Support\ApiResponse;

/**
 * @group Frontend
 * @subgroup Tenant
 */
class CurrentTenantController extends Controller
{
    /**
     * Current Tenant
     * @apiResource App\Http\Resources\TenantResource
     * @apiResourceModel App\Models\Tenant
     */
    public function __invoke()
    {
        return new ApiResponse(data: new TenantResource(currentTenant()));
    }
}
