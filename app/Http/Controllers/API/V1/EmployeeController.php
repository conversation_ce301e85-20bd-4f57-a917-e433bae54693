<?php

namespace App\Http\Controllers\API\V1;

use App\Http\Controllers\Controller;
use App\Http\Requests\NewEmployeeRequest;
use App\Http\Requests\UpdateEmployeeRequest;
use App\Http\Resources\EmployeeResource;
use App\Models\Employee;
use App\QueryBuilders\EmployeeQueryBuilder;
use App\Services\SyncEmployeeTagsService;
use App\Support\ApiResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

/**
 * @group Frontend
 * @subgroup Employees
 */
class EmployeeController extends Controller
{
    /**
     * List
     * @apiResource App\Http\Resources\EmployeeResource
     * @apiResourceModel App\Models\Employee
     */
    public function index(Request $request)
    {
        $request->validate([
            'filter.search' => ['nullable', 'string'],
            'filter.is_direct_manager' => ['nullable', 'boolean'],
            'filter.employees' => ['nullable', 'array'],
            'filter.employees.*' => ['string'],
            'filter.direct_managers' => ['nullable', 'array'],
            'filter.direct_managers.*' => ['string'],
            'filter.departments' => ['nullable', 'array'],
            'filter.departments.*' => ['string'],
            'filter.tags' => ['nullable', 'array'],
            'filter.tags.*' => ['integer'],
            'filter.exclude' => ['nullable'],
            'filter.is_active' => ['nullable'],
        ]);

        return new ApiResponse(
            data: EmployeeResource::collection(
                EmployeeQueryBuilder::query()
                    ->paginate($request->per_page)
                    ->withQueryString()
            )
        );
    }

    /**
     * Create
     * @apiResource App\Http\Resources\EmployeeResource
     * @apiResourceModel App\Models\Employee
     */
    public function store(NewEmployeeRequest $request): ApiResponse
    {
        $employee = DB::transaction(function () use ($request) {
            $attributes = $request->validated();

            $attributes['tenant_id'] = currentTenant()->id;

            $employee = Employee::create($attributes);

            if (isset($attributes['role_ids'])) {
                $employee->roles()->attach($attributes['role_ids']);
            }

            return (new SyncEmployeeTagsService($employee, $attributes['tags'] ?? []))->handle();
        });

        return new ApiResponse(
            data: new EmployeeResource($employee->load('roles')),
            message: __('New employee  has been created successfully')
        );
    }

    /**
     * Show
     * @apiResource App\Http\Resources\EmployeeResource
     * @apiResourceModel App\Models\Employee with=department,roles,tags,manager
     */
    public function show($employeeId)
    {
        return new ApiResponse(
            data: new EmployeeResource(
                currentTenant()
                    ->employees()
                    ->with(['department', 'roles', 'tags', 'manager'])
                    ->findOrFail($employeeId)
            )
        );
    }

    /**
     * Update
     * @apiResource App\Http\Resources\EmployeeResource
     * @apiResourceModel App\Models\Employee with=roles
     */
    public function update(UpdateEmployeeRequest $request, $employeeId)
    {
        $employee = DB::transaction(function () use ($employeeId, $request) {
            $attributes = $request->validated();
            $attributes['tenant_id'] = currentTenant()->id;
            $employee = currentTenant()->employees()->where('id', $employeeId)->firstOrFail();

            $employee->update($attributes);

            if (isset($attributes['role_ids'])) {
                $employee->roles()->sync($attributes['role_ids']);
            }

            return (new SyncEmployeeTagsService($employee, $attributes['tags'] ?? []))->handle();
        });

        return new ApiResponse(
            data: new EmployeeResource($employee->load('roles')),
            message: __('The Employee has been updated successfully.')
        );
    }
}
