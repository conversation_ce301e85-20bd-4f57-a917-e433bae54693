<?php

namespace App\Http\Controllers\API\V1;

use App\Http\Controllers\Controller;
use App\Http\Resources\SubscriptionResource;
use App\Support\ApiResponse;
use Illuminate\Http\Request;

/**
 * @group Frontend
 * @subgroup Tenant
 */
class GetCurrentTeamSubscriptionController extends Controller
{
    /**
     * Current Team Subscription
     * @apiResource App\Http\Resources\SubscriptionResource
     * @apiResourceModel App\Models\Subscription with=subscriptionItems,plan
     */
    public function __invoke(Request $request)
    {
        return new ApiResponse(
            data: new SubscriptionResource(
                currentTenant()->activeSubscription()->with('subscriptionItems', 'plan')->first()
            )
        );
    }
}
