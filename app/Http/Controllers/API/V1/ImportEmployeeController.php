<?php

namespace App\Http\Controllers\API\V1;

use App\Support\ApiResponse;
use App\Imports\EmployeeImport;
use Illuminate\Support\Facades\Log;
use App\Http\Controllers\Controller;
use Maatwebsite\Excel\Facades\Excel;
use App\Http\Requests\ImportEmployeeFromExcelRequest;

/**
 * @group Frontend
 * @subgroup Employees
 */
class ImportEmployeeController extends Controller
{
    /**
     * Employees - Import from Excel
     */
    public function __invoke(ImportEmployeeFromExcelRequest $request)
    {
        try {
            Excel::import(new EmployeeImport(), $request->file('file'));
        } catch (\Illuminate\Database\QueryException $e) {
            // for duplicates issue
            Log::error($e);
            app('sentry')->captureException($e);

            return new ApiResponse(message: $e->getMessage(), code: 400); // get a message of what was the issue
        }

        return new ApiResponse(message: __('CSV file imported successfully'));
    }
}
