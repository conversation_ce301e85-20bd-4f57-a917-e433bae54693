<?php

namespace App\Http\Controllers\API\V1;

use App\Http\Controllers\Controller;
use App\Http\Resources\RoleResource;
use App\Support\ApiResponse;
use function currentTenant;

/**
 * @group Frontend
 * @subgroup Roles
 */
class RoleController extends Controller
{
    /**
     * List
     * @apiResource App\Http\Resources\RoleResource
     * @apiResourceModel App\Models\Role with=software
     */
    public function index()
    {
        return new ApiResponse(
            data: RoleResource::collection(currentTenant()->rolesBySubscription())
        );
    }
}
