<?php

namespace App\Http\Controllers\API\V1;

use App\Http\Controllers\Controller;
use App\Http\Requests\UpdateEmployeePreferredLanguageRequest;
use App\Support\ApiResponse;

/**
 * @group Frontend
 * @subgroup Employees
 */
class SetEmployeePreferredLanguageController extends Controller
{
    /**
     * Set Current Preferred Language
     */
    public function __invoke(UpdateEmployeePreferredLanguageRequest $request): ApiResponse
    {
        auth()
            ->user()
            ->update(['preferred_language' => $request->language]);

        return new ApiResponse(message: __('employee preferred language updated successfully'));
    }
}
