<?php

namespace App\Http\Controllers\API\V1;

use App\Http\Controllers\Controller;
use App\Http\Resources\TagResource;
use App\Support\ApiResponse;
use Illuminate\Http\Request;
use Illuminate\Validation\Rule;
use <PERSON><PERSON>\QueryBuilder\AllowedFilter;
use <PERSON><PERSON>\QueryBuilder\QueryBuilder;

/**
 * @group Frontend
 * @subgroup Tags
 */
class TagController extends Controller
{
    /**
     * List
     * @apiResource App\Http\Resources\TagResource
     * @apiResourceModel App\Models\Tag
     */
    public function index(Request $request)
    {
        $request->validate([
            'filter.search' => ['nullable'],
            'per_page' => ['nullable', 'integer'],
        ]);

        return new ApiResponse(
            data: TagResource::collection(
                QueryBuilder::for(currentTenant()->tags())
                    ->allowedFilters([AllowedFilter::partial('search', 'name')])
                    ->withCount('employees')
                    ->defaultSort('-created_at')
                    ->paginate($request->per_page)
                    ->withQueryString()
            )
        );
    }

    /**
     * Create
     * @apiResource App\Http\Resources\TagResource
     * @apiResourceModel App\Models\Tag
     */
    public function store(Request $request)
    {
        $data = $request->validate([
            'name' => ['required', 'string', 'max:255', Rule::uniqueTenant('tags', 'name')],
            'color' => ['nullable', 'string', 'hex_color'],
        ]);

        $tag = currentTenant()->tags()->create($data);

        return new ApiResponse(
            data: new TagResource($tag),
            message: __('Tag created successfully')
        );
    }

    /**
     * Update
     * @apiResource App\Http\Resources\TagResource
     * @apiResourceModel App\Models\Tag
     */
    public function update(Request $request, $tag)
    {
        $tag = currentTenant()->tags()->findOrFail($tag);

        $data = $request->validate([
            'name' => [
                'required',
                'string',
                'max:255',
                Rule::uniqueTenant('tags', 'name')->ignore($tag),
            ],
            'color' => ['nullable', 'string', 'hex_color'],
        ]);

        $tag->update($data);

        return new ApiResponse(
            data: new TagResource($tag),
            message: __('Tag updated successfully')
        );
    }

    /**
     * Delete
     */
    public function destroy($tag)
    {
        $tag = currentTenant()->tags()->findOrFail($tag);

        // employees will be detached automatically because of cascade delete
        $tag->delete();

        return new ApiResponse(message: __('Tag deleted successfully'));
    }
}
