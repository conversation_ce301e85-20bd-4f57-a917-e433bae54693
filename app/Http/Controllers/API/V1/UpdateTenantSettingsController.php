<?php

namespace App\Http\Controllers\API\V1;

use App\Http\Controllers\Controller;
use App\Http\Requests\UpdateTenantSettingsRequest;
use App\Http\Resources\TenantResource;
use App\Support\ApiResponse;

/**
 * @group Frontend
 * @subgroup Tenants
 */
class UpdateTenantSettingsController extends Controller
{
    /**
     * Update settings
     * @apiResource App\Http\Resources\TenantResource
     * @apiResourceModel App\Models\Tenant
     */
    public function __invoke(UpdateTenantSettingsRequest $request): ApiResponse
    {
        $tenant = currentTenant();

        $tenant->update($request->validated());

        $tenant->refreshLogos();

        return new ApiResponse(
            data: new TenantResource($tenant),
            message: __('settings updated successfully')
        );
    }
}
