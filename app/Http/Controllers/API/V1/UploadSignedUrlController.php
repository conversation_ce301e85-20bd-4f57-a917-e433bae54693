<?php

namespace App\Http\Controllers\API\V1;

use App\Enums\Folder;
use App\Http\Controllers\Controller;
use App\Support\ApiResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Str;
use Illuminate\Validation\Rule;
use Storage;

/**
 * @group Frontend
 */
class UploadSignedUrlController extends Controller
{
    /**
     * Get an upload signed URL.
     * @response {"data":{"url":"https:\/\/example.com\/temp\/images\/file","storage_name":"file_name.png"}}
     */
    public function __invoke(Request $request)
    {
        $request->validate([
            'display_name' => ['required', 'string'],
            'folder' => ['required', 'string', Rule::enum(Folder::class)],
        ]);

        $extension = pathinfo($request->input('display_name'), PATHINFO_EXTENSION);

        $fileName = Str::uuid() . '.' . $extension;

        return new ApiResponse(
            data: [
                'url' => Storage::temporaryUploadUrl(
                    "temp/{$request->input('folder')}/$fileName",
                    now()->addHours(2)
                )['url'],
                'storage_name' => $fileName,
            ]
        );
    }
}
