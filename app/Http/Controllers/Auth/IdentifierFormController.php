<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Http\Requests\Auth\LoginRequest;
use App\Support\RedirectToExternalAuthenticator;
use App\Support\RedirectToOtpAuthenticator;
use Illuminate\Validation\ValidationException;

class IdentifierFormController extends Controller
{
    public function create()
    {
        loginSession()->clear();

        return inertia('Login/IdentifierForm');
    }

    public function store(LoginRequest $request)
    {
        loginSession()->start($request->input('identifier'));

        $tenant = loginSession()->resolveTenant();

        if (loginSession()->hasMultipleTenants()) {
            return redirect()->route('select-tenant');
        }

        if (loginSession()->allEmployeesInactive()) {
            throw ValidationException::withMessages([
                'identifier' => __('Your Account is not Active. Please Contact Your Administrator'),
            ]);
        }

        if (!$tenant) {
            throw ValidationException::withMessages([
                'identifier' => __('This Email does not exist in our systems'),
            ]);
        }

        if (!loginSession()->employee() && $tenant->hasInternalAuthenticator()) {
            throw ValidationException::withMessages([
                'identifier' => __('This Email does not exist in our systems'),
            ]);
        }

        if ($tenant->hasInternalAuthenticator()) {
            return RedirectToOtpAuthenticator::handle();
        }

        return RedirectToExternalAuthenticator::handle();
    }
}
