<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Http\Middleware\SetBrowserId;
use App\Services\RevokeTokenService;
use Illuminate\Http\Request;
use Lara<PERSON>\Passport\RefreshTokenRepository;
use Lara<PERSON>\Passport\Token;

class LogoutController extends Controller
{
    public function __invoke(Request $request, RefreshTokenRepository $refreshTokenRepository)
    {
        dd($request->cookie(SetBrowserId::BID));
        auth('web')
            ->user()
            ->tokens()
            ->with('client')
            ->each(function (Token $token) use ($refreshTokenRepository, $request) {
                (new RevokeTokenService($token, $request->cookie(SetBrowserId::BID)))->handle();
            });

        auth()->guard('web')->logout();

        session()->invalidate();

        session()->regenerateToken();

        return redirect()->route('identifier-form.create');
    }
}
