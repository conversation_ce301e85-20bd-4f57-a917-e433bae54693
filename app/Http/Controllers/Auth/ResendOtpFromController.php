<?php

namespace App\Http\Controllers\Auth;

use App\Authenticators\OneTimePasswordAuthenticator;
use App\Enums\IdentifierType;
use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Validation\Rule;

class ResendOtpFromController extends Controller
{
    public function __construct(private readonly OneTimePasswordAuthenticator $authenticator)
    {
    }

    public function __invoke(Request $request)
    {
        $request->validate([
            'identifier_type' => ['nullable', Rule::enum(IdentifierType::class)],
        ]);

        loginSession()->setIdentifierType(
            $request->input('identifier_type')
                ? IdentifierType::from($request->input('identifier_type'))
                : null
        );

        $this->authenticator->resendOtp(
            loginSession()->resolveTenant(),
            loginSession()->resolveIdentifier()
        );

        return redirect()
            ->route('otp.create')
            ->with('success', __('One time password resent successfully'));
    }
}
