<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Models\Tenant;
use App\Support\RedirectToExternalAuthenticator;
use App\Support\RedirectToOtpAuthenticator;
use Illuminate\Http\Request;
use Illuminate\Validation\ValidationException;
use Inertia\Inertia;

class SelectTenantController extends Controller
{
    public function create()
    {
        if (loginSession()->employeesOfIdentifier()->count() < 2) {
            return redirect()->route('identifier-form.create');
        }

        return Inertia::render('Login/SelectTenant', [
            'employees' => loginSession()->employeesOfIdentifier(),
        ]);
    }

    public function store(Request $request)
    {
        $request->validate([
            'tenant' => ['required', 'exists:tenants,id'],
        ]);

        loginSession()->setTenant(Tenant::with('authenticatorConfig')->find($request->tenant));

        $employee = loginSession()->employee();

        if (!$employee->is_active) {
            throw ValidationException::withMessages([
                'email' => __('Your Account is not Active. Please Contact Your Administrator'),
            ]);
        }

        $tenant = loginSession()->resolveTenant();

        if ($tenant->hasInternalAuthenticator()) {
            return RedirectToOtpAuthenticator::handle();
        }

        return RedirectToExternalAuthenticator::handle();
    }
}
