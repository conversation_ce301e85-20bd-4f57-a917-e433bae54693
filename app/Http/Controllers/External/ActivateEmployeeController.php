<?php

namespace App\Http\Controllers\External;

use App\Http\Controllers\Controller;
use App\Services\GetEmployeeFromRequestService;
use App\Support\ApiResponse;

/**
 * @group External
 * @subgroup Employees
 */
class ActivateEmployeeController extends Controller
{
    /**
     * Activate
     */
    public function __invoke(): ApiResponse
    {
        $employee = (new GetEmployeeFromRequestService())->handle();

        $employee->update(['is_active' => true]);

        return new ApiResponse(message: 'Employee activated successfully');
    }
}
