<?php

namespace App\Http\Controllers\External;

use App\Http\Controllers\Controller;
use App\Services\GetEmployeeFromRequestService;
use App\Support\ApiResponse;

/**
 * @group External
 * @subgroup Employees
 */
class DeactivateEmployeeController extends Controller
{
    /**
     * Deactivate
     */
    public function __invoke(): ApiResponse
    {
        $employee = (new GetEmployeeFromRequestService())->handle();

        $employee->update(['is_active' => false]);

        return new ApiResponse(message: 'Employee deactivated successfully');
    }
}
