<?php

namespace App\Http\Controllers\External;

use App\Enums\EmployeePreferredLanguage;
use App\Http\Controllers\Controller;
use App\Http\Requests\External\ShowEmployeeRequest;
use App\Http\Requests\External\StoreEmployeeRequest;
use App\Http\Requests\External\UpdateEmployeeRequest;
use App\Http\Resources\EmployeeResource;
use App\Models\Employee;
use App\Models\Role;
use App\Services\GetEmployeeFromRequestService;
use App\Support\ApiResponse;
use Illuminate\Support\Facades\DB;

/**
 * @group External
 * @subgroup Employees
 */
class EmployeeController extends Controller
{
    /**
     * List
     * @apiResource App\Http\Resources\EmployeeResource
     * @apiResourceModel App\Models\Employee
     */
    public function index(): ApiResponse
    {
        return new ApiResponse(
            data: EmployeeResource::collection(
                currentTenant()
                    ->employees()
                    ->with(['department', 'tags'])
                    ->get()
            )
        );
    }

    /**
     * Create
     * @apiResource App\Http\Resources\EmployeeResource
     * @apiResourceModel App\Models\Employee
     */
    public function store(StoreEmployeeRequest $request): ApiResponse
    {
        $employee = DB::transaction(function () use ($request) {
            $employee = Employee::create([
                'first_name' => $request['first_name'],
                'last_name' => $request['last_name'],
                'email' => $request['email'],
                'number' => $request['number'],
                'phone' => $request['phone'] ?? null,
                'position' => $request['position'],
                'tenant_id' => auth()->id(),
                'is_active' => $request['is_active'] ?? true,
                'preferred_language' =>
                    $request['preferred_language'] ?? EmployeePreferredLanguage::Arabic,
                'department_id' => $request->department_id,
                'manager_id' => $request->manager_id,
            ]);

            if ($request->has('roles')) {
                ray($request->array('roles'));
                $employee
                    ->roles()
                    ->attach(Role::whereIn('name', $request->array('roles'))->pluck('uuid'));
            }

            return $employee;
        });

        return new ApiResponse(
            data: new EmployeeResource($employee->load(['department', 'roles'])),
            message: 'new employee created successfully'
        );
    }

    /**
     * Show
     * @apiResource App\Http\Resources\EmployeeResource
     * @apiResourceModel App\Models\Employee
     */
    public function show(ShowEmployeeRequest $request): ApiResponse
    {
        // /employees/{employee}
        $employeeFromParam = (new GetEmployeeFromRequestService(
            required: (bool) $request->route('employee')
        ))->handle();

        // /employees/get?id=1
        $employee =
            $employeeFromParam ??
            currentTenant()
                ->employees()
                ->when($request->id, fn($q) => $q->where('id', $request->id))
                ->when($request->email, fn($q) => $q->where('email', $request->email))
                ->when($request->number, fn($q) => $q->where('number', $request->number))
                ->firstOrFail();

        return new ApiResponse(
            data: new EmployeeResource($employee->load(['roles', 'department']))
        );
    }

    /**
     * Update
     * @apiResource App\Http\Resources\EmployeeResource
     * @apiResourceModel App\Models\Employee
     */
    public function update(UpdateEmployeeRequest $request): ApiResponse
    {
        $employee = DB::transaction(function () use ($request) {
            $employee = (new GetEmployeeFromRequestService())->handle();

            $employee->update([
                ...$request->validated(),
                'department_id' => $request->department_id,
                'manager_id' => $request->manager_id,
            ]);

            if ($request->validated('roles') ?? []) {
                $employee
                    ->roles()
                    ->sync(
                        Role::whereIn('name', $request->validated('roles') ?? [])->pluck('uuid')
                    );
            }

            return $employee;
        });

        return new ApiResponse(
            data: new EmployeeResource($employee->load(['department', 'roles'])),
            message: 'employee updated successfully'
        );
    }
}
