<?php

namespace App\Http\Controllers\External;

use App\Http\Controllers\Controller;
use App\Http\Requests\External\AttachEmployeeRolesRequest;
use App\Http\Requests\External\DetachEmployeeRolesRequest;
use App\Models\Role;
use App\Services\GetEmployeeFromRequestService;
use App\Support\ApiResponse;
use Illuminate\Support\Facades\DB;

/**
 * @group External
 * @subgroup Employees
 */
class EmployeeRoleController extends Controller
{
    /**
     * Attach roles to employee
     */
    public function attach(AttachEmployeeRolesRequest $request): ApiResponse
    {
        $employee = (new GetEmployeeFromRequestService())->handle();
        $data = $request->validated();

        DB::transaction(function () use ($employee, $data) {
            if (!empty($data['roles'])) {
                $roleIds = Role::whereIn('name', $data['roles'])->pluck('uuid');
                $employee->roles()->attach($roleIds);
            } elseif ($data['all'] ?? false) {
                $roleIds = currentTenant()->rolesBySubscription()->pluck('uuid');
                $employee->roles()->attach($roleIds);
            }
        });

        return new ApiResponse(message: 'Roles attached to employee successfully');
    }

    /**
     * Detach roles from employee
     */
    public function detach(DetachEmployeeRolesRequest $request): ApiResponse
    {
        $employee = (new GetEmployeeFromRequestService())->handle();
        $data = $request->validated();

        DB::transaction(function () use ($employee, $data) {
            if (!empty($data['roles'])) {
                $roleIds = Role::whereIn('name', $data['roles'])->pluck('uuid');
                $employee->roles()->detach($roleIds);
            } elseif ($data['all'] ?? false) {
                $employee->roles()->detach();
            }
        });

        return new ApiResponse(message: 'Roles detached from employee successfully');
    }
}