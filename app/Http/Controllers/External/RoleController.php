<?php

namespace App\Http\Controllers\External;

use App\Http\Controllers\Controller;
use App\Http\Resources\RoleResource;
use App\Support\ApiResponse;
use function currentTenant;

/**
 * @group External
 * @subgroup Roles
 */
class RoleController extends Controller
{
    /**
     * List
     */
    public function index()
    {
        return new ApiResponse(
            data: RoleResource::collection(currentTenant()->rolesBySubscription())
        );
    }
}
