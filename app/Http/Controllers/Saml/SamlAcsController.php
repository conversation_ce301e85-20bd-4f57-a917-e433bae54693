<?php

namespace App\Http\Controllers\Saml;

use App\Http\Controllers\Controller;
use App\Models\Employee;
use App\Models\Tenant;
use App\Services\Saml\SamlReceiveResponseService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;

class SamlAcsController extends Controller
{
    /**
     * Handle the incoming request.
     */
    public function __invoke($tenant, Request $request)
    {
        Log::info('Start SAML ACS proccess');
        $tenant = Tenant::active()->findOrFail($tenant);
        /**
         * @var \App\Models\TenantAuthenticatorConfig
         */
        $tenantAuthenticatorConfig = $tenant->authenticatorConfig;

        // TODO: handel all exceptions
        $receiveResponse = new SamlReceiveResponseService($request, $tenantAuthenticatorConfig);

        $nameId = $receiveResponse->getNameId();
        if ($tenantAuthenticatorConfig->useNameIdForAuthentication) {
            // if name id is an email address use it form authentication.
            $email = strtolower($nameId->getValue());
        } else {
            $email = strtolower(
                $receiveResponse
                    ->getAttribute($tenantAuthenticatorConfig->emailClaim)
                    ->getFirstAttributeValue()
            );
        }

        // We can store the id of the response to prevent replay attack, but no need because we are validating the assertion time.

        Log::info('Login success with attributes');
        Log::info($receiveResponse->getAttributes());

        /**
         * @var \App\Models\Employee
         */
        $employee = $tenant->employees()->where('email', $email)->first();

        if ($employee && !$employee->is_active) {
            return 'Error: The user is not active';
        }

        $attriubtes = [
            'first_name' => $receiveResponse
                ->getAttribute($tenantAuthenticatorConfig->firstNameClaim)
                ?->getFirstAttributeValue(),
            'last_name' => $receiveResponse
                ->getAttribute($tenantAuthenticatorConfig->lastNameClaim)
                ?->getFirstAttributeValue(),
            'position' => $receiveResponse
                ->getAttribute($tenantAuthenticatorConfig->positionClaim)
                ?->getFirstAttributeValue(),
            'number' => $receiveResponse
                ->getAttribute($tenantAuthenticatorConfig->employeeNumberClaim)
                ?->getFirstAttributeValue(),
            'phone' =>
                $receiveResponse
                    ->getAttribute($tenantAuthenticatorConfig->phoneClaim)
                    ?->getFirstAttributeValue() ?? null,
        ];

        if (
            $departmentName = $receiveResponse
                ->getAttribute($tenantAuthenticatorConfig->departmentNameClaim)
                ?->getFirstAttributeValue()
        ) {
            $department = $tenant->departments()->where('name', $departmentName)->first();
            if ($department) {
                $attriubtes['department_id'] = $department->id;
            }
        }

        if (!$employee) {
            Log::info('Employee not found');

            $domain = explode('@', $email)[1];
            if ($domain !== $tenant->domain) {
                // TODO: Throw an error
                return 'Error: The domain of the team is not right, team domain:' .
                    $tenant->domain .
                    ', given: ' .
                    $domain;
            }

            $attriubtes['tenant_id'] = $tenant->id;
            $attriubtes['email'] = $email;

            // create an employee
            $employee = Employee::create($attriubtes);
        } else {
            $employee->update($attriubtes);
        }

        Auth::login($employee);

        session()->regenerate();

        Log::info('End SAML ACS proccess');

        return redirect()->intended('/');
    }
}
