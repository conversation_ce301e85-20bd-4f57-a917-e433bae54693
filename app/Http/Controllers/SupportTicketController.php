<?php

namespace App\Http\Controllers;

use App\Http\Requests\SupportTicketRequest;
use App\Models\SupportTicket;
use App\Support\ApiResponse;

class SupportTicketController extends Controller
{
    public function store(SupportTicketRequest $request)
    {
        SupportTicket::create([
            ...$request->validated(),
            'tenant_id' => auth()->user()->tenant_id,
            'employee_id' => auth()->user()->id,
        ]);

        return new ApiResponse(
            message: __(
                'Your message has been sent successfully. Track its progress via your email'
            )
        );
    }
}
