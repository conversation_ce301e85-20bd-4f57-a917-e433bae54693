<?php

namespace App\Http\Middleware;

use App\Models\Tenant;
use Closure;
use Illuminate\Http\Request;

class Localization
{
    public function handle(Request $request, Closure $next)
    {
        $allowedLanguages = ['ar', 'en'];

        // external routes should always be in English
        if ($request->is('api/*/external/*') || $request->user() instanceof Tenant) {
            app()->setLocale('en');

            return $next($request);
        }

        $locale =
            $request->header('Accept-Language') ??
            ($request->user()->preferred_language->value ?? 'ar');

        $locale = in_array($locale, $allowedLanguages) ? $locale : 'ar';

        app()->setLocale($locale);

        return $next($request);
    }
}
