<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Str;

class SetBrowserId
{
    const BID = 'BID';

    public function handle(Request $request, Closure $next)
    {
        // IMPORTANT: BID is public and used ONLY in logout
        // and not safe to use in login or other critical/sensitive operations
        if (!$request->cookie(self::BID)) {
            cookie()->queue(
                cookie()->forever(
                    name: self::BID,
                    value: Str::uuid(),
                    domain: config('app.global_cookie_domain')
                )
            );
        }

        return $next($request);
    }
}
