<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Sentry\State\Scope;

class SetSentryUser
{
    public function handle(Request $request, Closure $next)
    {
        if (app()->bound('sentry')) {
            app('sentry')->configureScope(function (Scope $scope): void {
                [$user, $guard] = $this->user();

                if ($user) {
                    $scope->setUser([
                        'id' => $user->id,
                        'email' => $user->email,
                        'tenant_id' => $user->team?->nawart_uuid,
                        'guard' => $guard,
                    ]);
                }
            });
        }

        return $next($request);
    }

    public function user(): ?array
    {
        foreach (config('auth.guards') as $guard => $config) {
            // ignore any errors that might occur when calling auth()->user()
            $user = rescue(callback: fn() => auth($guard)->user(), report: false);

            if ($user) {
                return [$user, $guard];
            }
        }

        return null;
    }
}
