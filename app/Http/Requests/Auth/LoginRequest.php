<?php

namespace App\Http\Requests\Auth;

use App\DTOs\Identifier;
use Illuminate\Foundation\Http\FormRequest;
use Propaganistas\LaravelPhone\Rules\Phone;

class LoginRequest extends FormRequest
{
    public function rules(): array
    {
        return [
            'identifier' => [
                'required',
                'string',
                Identifier::hasDomain($this->identifier) ? 'email' : (new Phone())->country('SA'),
            ],
        ];
    }
}
