<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class BulkUpdateEmployeesDepartmentRequest extends FormRequest
{
    public function rules(): array
    {
        return [
            'all' => [Rule::requiredIf(!$this->employees), 'boolean'],
            'employees' => [
                Rule::requiredIf(!$this->all),
                'array',
                Rule::existsTenant('employees', 'id'),
            ],
        ];
    }
}
