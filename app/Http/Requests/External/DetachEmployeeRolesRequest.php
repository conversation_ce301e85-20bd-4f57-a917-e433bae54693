<?php

namespace App\Http\Requests\External;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class DetachEmployeeRolesRequest extends FormRequest
{
    public function rules(): array
    {
        return [
            'roles' => ['required_without:all', 'array'],
            'roles.*' => ['required', Rule::exists('roles', 'name')],
            'all' => ['required_without:roles', 'boolean'],
        ];
    }
}