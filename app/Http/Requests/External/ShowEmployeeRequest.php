<?php

namespace App\Http\Requests\External;

use Illuminate\Foundation\Http\FormRequest;

class ShowEmployeeRequest extends FormRequest
{
    public function rules(): array
    {
        return $this->route('employee')
            ? []
            : [
                'id' => ['required_without_all:email,number', 'string'],
                'email' => ['required_without_all:id,number', 'email'],
                'number' => ['required_without_all:id,email', 'string'],
            ];
    }
}
