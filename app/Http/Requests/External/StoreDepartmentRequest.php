<?php

namespace App\Http\Requests\External;

use App\Validation\ValidateManagerNameOrId;
use App\Validation\ValidateParentDepartmentNameOrId;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class StoreDepartmentRequest extends FormRequest
{
    public function rules(): array
    {
        return [
            'name' => ['required', 'string', 'max:255', Rule::uniqueTenant('departments')],
            'manager_email' => [
                'sometimes',
                'email',
                'string',
                'max:255',
                Rule::existsTenant('employees', 'email'),
            ],
            'manager_id' => ['sometimes', Rule::existsTenant('employees', 'id')],
            'parent_name' => ['sometimes', 'string', 'max:255'],
            'parent_id' => ['sometimes', Rule::existsTenant('departments', 'id')],
        ];
    }

    public function after(): array
    {
        return [new ValidateParentDepartmentNameOrId(), new ValidateManagerNameOrId()];
    }
}
