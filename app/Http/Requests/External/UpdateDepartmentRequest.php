<?php

namespace App\Http\Requests\External;

use App\Validation\ValidateManagerNameOrId;
use App\Validation\ValidateParentDepartmentNameOrId;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class UpdateDepartmentRequest extends FormRequest
{
    public function rules(): array
    {
        return [
            'name' => [
                'sometimes',
                'string',
                'max:255',
                Rule::uniqueTenant('departments')->ignore($this->route('department')),
            ],
            'manager_email' => [
                'sometimes',
                'email',
                'string',
                'max:255',
                Rule::existsTenant('employees', 'email'),
            ],
            'parent_name' => ['sometimes', 'string', 'max:255'],
            'manager_id' => ['sometimes', Rule::existsTenant('employees', 'id')],
            'parent_id' => ['sometimes', Rule::existsTenant('departments', 'id')],
        ];
    }

    public function after(): array
    {
        return [new ValidateParentDepartmentNameOrId(), new ValidateManagerNameOrId()];
    }
}
