<?php

namespace App\Http\Requests;

use Illuminate\Contracts\Database\Query\Builder;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class NewDepartmentRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return auth()->user()->hasRole('start-admin');
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'name' => [
                'required',
                'string',
                'max:255',
                Rule::unique('departments')->where(
                    fn(Builder $query) => $query->where('tenant_id', currentTenant()->id)
                ),
            ],
            'manager_id' => [
                'sometimes',
                Rule::exists('employees', 'id')->where(
                    fn(Builder $query) => $query->where('tenant_id', currentTenant()->id)
                ),
            ],
            'parent_id' => [
                'sometimes',
                Rule::exists('departments', 'id')->where(
                    fn(Builder $query) => $query->where('tenant_id', currentTenant()->id)
                ),
            ],
        ];
    }
}
