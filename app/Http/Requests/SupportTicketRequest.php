<?php

namespace App\Http\Requests;

use App\Enums\Folder;
use App\Enums\SupportTicketSource;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class SupportTicketRequest extends FormRequest
{
    public function rules(): array
    {
        return [
            'title' => ['required', 'string', 'max:255'],
            'description' => ['required', 'string'],
            'attachment' => ['nullable', 'string'],
            'source' => ['required', Rule::enum(SupportTicketSource::class)],
        ];
    }

    protected function passedValidation(): void
    {
        Folder::permanentlyStoreFile(Folder::SUPPORT_TICKET_ATTACHMENT, $this->input('attachment'));
    }
}
