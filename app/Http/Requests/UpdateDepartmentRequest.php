<?php

namespace App\Http\Requests;

use Illuminate\Contracts\Database\Query\Builder;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class UpdateDepartmentRequest extends FormRequest
{
    public function rules(): array
    {
        return [
            'name' => [
                'required',
                'string',
                'max:255',
                Rule::unique('departments')
                    ->ignore($this->route('departmentId'))
                    ->where(fn(Builder $query) => $query->where('tenant_id', currentTenant()->id)),
            ],
            'manager_id' => [
                'required',
                Rule::exists('employees', 'id')->where(
                    fn(Builder $query) => $query->where('tenant_id', currentTenant()->id)
                ),
            ],
            'parent_id' => [
                'sometimes',
                Rule::exists('departments', 'id')->where(
                    fn(Builder $query) => $query->where('tenant_id', currentTenant()->id)
                ),
            ],
        ];
    }
}
