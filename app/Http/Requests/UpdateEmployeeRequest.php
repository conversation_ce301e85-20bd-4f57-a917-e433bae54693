<?php

namespace App\Http\Requests;

use App\Enums\EmployeePreferredLanguage;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;
use Propaganistas\LaravelPhone\Rules\Phone;

class UpdateEmployeeRequest extends FormRequest
{
    protected function prepareForValidation(): void
    {
        if (phone($this->phone, 'SA')->isValid()) {
            $this->merge([
                'phone' => phone($this->phone, 'SA')->formatE164(),
            ]);
        }

        // otherwise, keep the original value, it will fail validation later,
        // or it's not provided, which is fine
    }

    public function rules(): array
    {
        $employeeId = $this->route('employeeId');

        return [
            'first_name' => ['sometimes', 'max:255'],
            'last_name' => ['sometimes', 'max:255'],
            'phone' => [
                'sometimes',
                'nullable',
                'numeric',
                (new Phone())->country('SA'),
                Rule::uniqueTenant('employees', 'phone')->ignore($employeeId),
            ],
            'email' => [
                // todo: make it required after updating frontend
                'sometimes',
                'email',
                Rule::uniqueTenant('employees', 'email')->ignore($employeeId),
            ],
            'role_ids' => ['sometimes', 'array'],
            'role_ids.*' => ['required', 'exists:roles,uuid'],
            'tags' => ['sometimes', 'array'],
            'tags.*.name' => ['required', 'string', 'distinct'],
            'tags.*.color' => ['required', 'string', 'hex_color'],
            'position' => ['sometimes', 'max:255'],
            'number' => [
                'sometimes',
                Rule::uniqueTenant('employees', 'number')->ignore($employeeId),
            ],
            'department_id' => ['nullable', Rule::existsTenant('departments', 'id')],
            'manager_id' => ['nullable', Rule::existsTenant('employees', 'id')],
            'preferred_language' => [
                'sometimes',
                'string',
                Rule::enum(EmployeePreferredLanguage::class),
            ],
        ];
    }
}
