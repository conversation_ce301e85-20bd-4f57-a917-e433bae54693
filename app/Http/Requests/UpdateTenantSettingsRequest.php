<?php

namespace App\Http\Requests;

use App\Enums\Folder;
use Illuminate\Foundation\Http\FormRequest;
use Storage;
use function abort_if;
use function app;
use function currentTenant;

class UpdateTenantSettingsRequest extends FormRequest
{
    protected array $fileKeys = ['colored_logo', 'white_logo'];

    public function rules(): array
    {
        return [
            'name' => ['sometimes', 'string', 'max:255'],
            'color' => ['sometimes', 'string', 'max:255', 'hex_color'],
            // three cases for the logos
            // if the logo is not passed (not part of the request body), nothing will happen
            // if the logo is null, it will delete the current logo
            // if the logo is URL, it will update to a new logo
            'colored_logo' => ['sometimes', 'nullable', 'string'],
            'white_logo' => ['sometimes', 'nullable', 'string'],
        ];
    }

    protected function passedValidation(): void
    {
        $tenant = currentTenant();

        foreach ($this->fileKeys as $fileKey) {
            if ($this->missing($fileKey)) {
                continue;
            }

            // first, let's delete the existing file if it exists
            if ($tenant->$fileKey) {
                Storage::delete(Folder::LOGOS->path($tenant->$fileKey));
            }

            $uploadedFileUrl = $this->input($fileKey);

            if (!$uploadedFileUrl) {
                continue;
            }

            $temporaryFileName = Folder::LOGOS->tempPath($uploadedFileUrl);

            abort_if(
                !Storage::exists($temporaryFileName) && !app()->runningUnitTests(),
                400,
                "File [$fileKey] is not uploaded"
            );

            $permanentFilePath = Folder::LOGOS->path($uploadedFileUrl);

            Storage::move($temporaryFileName, $permanentFilePath);
        }
    }
}
