<?php

namespace App\Http\Resources;

use App\Models\Role;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/** @mixin Role */
class RoleResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->uuid,
            'name' => $this->name,
            'readable_name' => $this->display_name,
            'description' => $this->description,
            'application' => $this->whenLoaded(
                'software',
                fn() => $this->software->code->translated()
            ),
        ];
    }
}
