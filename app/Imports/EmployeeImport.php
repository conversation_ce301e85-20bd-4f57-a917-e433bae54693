<?php

namespace App\Imports;

use App\Models\Department;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Maatwebsite\Excel\Concerns\SkipsEmptyRows;
use Maatwebsite\Excel\Concerns\ToCollection;
use Maatwebsite\Excel\Concerns\WithHeadingRow;
use Maatwebsite\Excel\Concerns\WithValidation;
use function auth;
use function currentTenant;

class EmployeeImport implements SkipsEmptyRows, ToCollection, WithHeadingRow, WithValidation
{
    public function collection(Collection $collection): void
    {
        DB::transaction(function () use ($collection) {
            $this->createEmployees($collection);
            $this->assignEmployeesToDirectManager($collection);
        });
    }

    public function prepareForValidation($data)
    {
        // Remove white spaces from email
        $data['email'] = strtolower(preg_replace('/\s+/', '', $data['email']));

        if (isset($data['manager_email'])) {
            // Remove white spaces from email
            $data['manager_email'] = $data['manager_email']
                ? strtolower(preg_replace('/\s+/', '', $data['manager_email']))
                : null;
        }

        // Format phone number
        if (isset($data['phone']) && phone($data['phone'], 'SA')->isValid()) {
            $data['phone'] = phone($data['phone'], 'SA')->formatE164();
        }

        return $data;
    }

    public function rules(): array
    {
        return [
            'first_name' => ['required', 'max:255'],
            'last_name' => ['nullable', 'max:255'],
            'manager_email' => ['nullable', 'max:255', 'email'],
            'phone' => ['nullable', 'phone'],
            'email' => ['required', 'email'],
            'position' => ['nullable', 'max:255'],
            'number' => ['nullable'],
        ];
    }

    public function assignEmployeesToDirectManager(Collection $collection): void
    {
        $collection->whereNotNull('manager_email')->each(function (Collection $rawEmployee) {
            $employee = currentTenant()
                ->employees()
                ->firstWhere('email', $rawEmployee['email']);
            $manager = currentTenant()
                ->employees()
                ->firstWhere('email', $rawEmployee['manager_email']);

            if ($employee && $manager) {
                $employee->update(['manager_id' => $manager->id]);
            }
        });
    }

    public function createEmployees(Collection $collection): void
    {
        $collection->each(function (Collection $rawEmployee) {
            $attributes = [
                'email' => $rawEmployee['email'],
                'number' => $rawEmployee['number'] ?? null,
                'first_name' => $rawEmployee['first_name'],
                'last_name' => $rawEmployee['last_name'] ?? null,
                'phone' => $rawEmployee['phone'] ?? null,
                'position' => $rawEmployee['position'] ?? null,
                'department_id' => $this->getDepartment($rawEmployee['department'])['id'] ?? null,
            ];

            $employee = currentTenant()
                ->employees()
                ->firstWhere('email', $attributes['email']);

            if ($employee) {
                $employee->update($attributes);
                return;
            }

            currentTenant()->employees()->create($attributes);
        });
    }

    public function getDepartment(?string $rawDepartment): ?Department
    {
        return $rawDepartment
            ? currentTenant()
                ->departments()
                ->firstOrCreate(
                    [
                        'name' => $rawDepartment,
                        'tenant_id' => currentTenant()->id,
                    ],
                    [
                        'manager_id' => auth()->id(),
                        'tenant_id' => currentTenant()->id,
                    ]
                )
            : null;
    }
}
