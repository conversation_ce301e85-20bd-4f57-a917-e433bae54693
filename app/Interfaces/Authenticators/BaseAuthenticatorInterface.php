<?php

namespace App\Interfaces\Authenticators;

use App\DTOs\Identifier;
use App\Models\Tenant;
use App\Models\TenantAuthenticatorConfig;

interface BaseAuthenticatorInterface
{
    public function configure(TenantAuthenticatorConfig $config): void;

    public function authenticate(
        Identifier $identifier,
        Tenant $tenant,
        ?string $password = null
    ): bool; // email, mobile etc

    public function errors(): array;
    // get the authenticator of the chosen team // default password
    // configure the authenticator from the database
    // call authenticate with request as a param
    // if authenticate will check if the request
}
