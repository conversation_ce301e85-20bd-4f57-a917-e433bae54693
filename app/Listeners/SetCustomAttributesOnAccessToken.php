<?php

namespace App\Listeners;

use <PERSON><PERSON>\Passport\Events\AccessTokenCreated;
use <PERSON><PERSON>\Passport\Token;
use <PERSON><PERSON><PERSON>\Location\Facades\Location;

class SetCustomAttributesOnAccessToken
{
    public function handle(AccessTokenCreated $event): void
    {
        $tokenId = $event->tokenId;

        $token = Token::find($tokenId);

        $ip = request()->getClientIp();

        //        $location = Location::get($ip);

        //        $locationString = $location ? $location->countryName.' '.$location->regionName.' '.$location->cityName : null;

        $token->update([
            'device_id' => request()->userAgent(),
            'ip_address' => $ip,
            //            'location' => $locationString,
            'last_used_at' => now(),
        ]);
    }
}
