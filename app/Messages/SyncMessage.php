<?php

namespace App\Messages;

use App\Enums\SyncName;
use Exception;
use Prwnr\Streamer\Contracts\Event;
use Prwnr\Streamer\Facades\Streamer;

class SyncMessage implements Event
{
    public function __construct(protected array $data, protected SyncName $syncName)
    {
    }

    public function name(): string
    {
        return $this->syncName->value;
    }

    public function type(): string
    {
        return Event::TYPE_EVENT;
    }

    public function payload(): array
    {
        return $this->data;
    }

    public function send(): void
    {
        info("Sending sync message [{$this->name()}]");

        if (!isset($this->data['id'])) {
            throw new Exception(
                'The id field is required to send a sync message: ' . $this->name()
            );
        }

        if (!app()->runningUnitTests()) {
            Streamer::emit($this);
        }

        info("Sync message sent [{$this->name()}]");
    }
}
