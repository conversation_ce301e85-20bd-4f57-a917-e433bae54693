<?php

namespace App\Models;

use App\DTOs\Identifier;
use App\Enums\EmployeePreferredLanguage;
use Arr;
use Illuminate\Contracts\Translation\HasLocalePreference;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasManyThrough;
use Illuminate\Database\Eloquent\Relations\HasOneThrough;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Laravel\Nova\Auth\Impersonatable;
use Laravel\Passport\HasApiTokens;
use OwenIt\Auditing\Auditable;
use OwenIt\Auditing\Contracts\Auditable as AuditableContract;
use Propaganistas\LaravelPhone\Casts\E164PhoneNumberCast;
use Propaganistas\LaravelPhone\PhoneNumber;
use Spatie\Permission\Traits\HasRoles;
use function blank;
use function collect;

/**
 * @property PhoneNumber $phone
 */
class Employee extends Authenticatable implements AuditableContract, HasLocalePreference
{
    use Auditable;
    use HasApiTokens;
    use HasFactory;
    use HasRoles;
    use HasUuids;
    use Impersonatable;
    use Notifiable;

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'first_name',
        'last_name',
        'email',
        'password',
        'position',
        'number',
        'phone',
        'department_id',
        'manager_id',
        'tenant_id',
        'is_active',
        'preferred_language',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array
     */
    protected $hidden = ['password'];

    protected $casts = [
        'preferred_language' => EmployeePreferredLanguage::class,
        'phone' => E164PhoneNumberCast::class . ':SA',
    ];

    protected $appends = ['name'];

    protected function email(): Attribute
    {
        return Attribute::make(set: fn(string $value) => strtolower($value));
    }

    protected function name(): Attribute
    {
        return Attribute::get(fn() => "$this->first_name $this->last_name");
    }

    public function scopeIdentifier(Builder $query, Identifier $identifier): Builder
    {
        return $query
            ->whereNotNull($identifier->type()->value)
            ->where($identifier->type()->value, $identifier->value());
    }

    public function scopeSearch(Builder $q, array|string|null $search = null): Builder
    {
        if (blank($search)) {
            return $q;
        }

        $searchableColumns = ['first_name', 'last_name', 'email', 'number'];

        return $q->where(
            fn($q) => collect($search)
                ->filter()
                ->map('strtolower')
                ->each(fn($text) => $q->orWhereAny($searchableColumns, 'LIKE', "%$text%"))
        );
    }

    public function scopeFilterByTags(Builder $query, string|array|null $tags = null): Builder
    {
        return $query->when(
            $tags,
            fn(Builder $query) => $query->whereHas(
                'tags',
                fn(Builder $query) => $query->whereIn('tags.id', Arr::wrap($tags))
            )
        );
    }

    public function scopeFilterByDepartments(
        Builder $query,
        string|array|null $departments = null
    ): Builder {
        return $query->when(
            $departments,
            fn(Builder $query) => $query->whereHas(
                'department',
                fn(Builder $query) => $query->whereIn('departments.id', Arr::wrap($departments))
            )
        );
    }

    public function scopeActive(Builder $query): Builder
    {
        return $query->where('is_active', true);
    }

    public function department(): BelongsTo
    {
        return $this->belongsTo(Department::class, 'department_id');
    }

    public function directManager(): BelongsTo
    {
        return $this->belongsTo(Employee::class, 'manager_id');
    }

    public function directEmployees(): HasMany
    {
        return $this->hasMany(Employee::class, 'manager_id');
    }

    public function manager(): HasOneThrough
    {
        return $this->hasOneThrough(
            Employee::class,
            EmployeeManagerView::class,
            'employee_id',
            'id',
            'id',
            'current_manager_id'
        );
    }

    public function managedEmployees(): HasManyThrough
    {
        return $this->hasManyThrough(
            Employee::class,
            EmployeeManagerView::class,
            'current_manager_id',
            'id',
            'id',
            'employee_id'
        );
    }

    public function tenant(): BelongsTo
    {
        return $this->belongsTo(Tenant::class);
    }

    public function preferredLocale(): string
    {
        return $this->preferred_language->value;
    }

    public function tags()
    {
        return $this->morphToMany(Tag::class, 'taggable');
    }

    public function supportTickets(): HasMany
    {
        return $this->hasMany(SupportTicket::class);
    }

    // see: https://spatie.be/docs/laravel-permission/v6/basic-usage/multiple-guards#content-forcing-use-of-a-single-guard
    protected function getDefaultGuardName(): string
    {
        return 'web';
    }
}
