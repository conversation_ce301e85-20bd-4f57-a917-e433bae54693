<?php

namespace App\Models\Passport;

use Laravel\Passport\Client as PassportClient;
use OwenIt\Auditing\Auditable;
use OwenIt\Auditing\Contracts\Auditable as AuditableContract;

class Client extends PassportClient implements AuditableContract
{
    use Auditable;

    /**
     * Determine if the client should skip the authorization prompt.
     */
    public function skipsAuthorization(): bool
    {
        return true; // $this->firstParty();
    }
}
