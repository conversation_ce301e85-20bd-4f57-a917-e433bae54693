<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use OwenIt\Auditing\Auditable;
use OwenIt\Auditing\Contracts\Auditable as AuditableContract;

class Plan extends Model implements AuditableContract
{
    use Auditable, HasFactory, HasUuids;

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = ['name', 'is_active', 'is_custom', 'customized_for_tenant_id', 'price'];

    /**
     * The attributes that should be cast to native types.
     *
     * @var array
     */
    protected $casts = [
        'is_active' => 'boolean',
        'is_custom' => 'integer',
        'price' => 'float',
    ];

    public function customizedForTenant(): BelongsTo
    {
        return $this->belongsTo(Tenant::class);
    }

    public function planItems()
    {
        return $this->hasMany(PlanItem::class);
    }
}
