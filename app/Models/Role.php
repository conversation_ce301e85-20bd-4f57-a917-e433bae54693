<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Spatie\Permission\Models\Role as SpatieRole;

class Role extends SpatieRole
{
    use HasFactory, HasUuids;

    protected $primaryKey = 'uuid';

    protected $casts = [
        'uuid' => 'string',
    ];

    protected function displayName(): Attribute
    {
        return Attribute::get(fn() => __($this->name));
    }

    protected function description(): Attribute
    {
        return Attribute::get(fn() => __("$this->name-description"));
    }

    public function software(): BelongsTo
    {
        return $this->belongsTo(Software::class);
    }
}
