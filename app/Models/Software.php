<?php

namespace App\Models;

use App\Enums\SoftwareCode;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use OwenIt\Auditing\Auditable;
use OwenIt\Auditing\Contracts\Auditable as AuditableContract;

class Software extends Model implements AuditableContract
{
    use Auditable, HasFactory, HasUuids;

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = ['name', 'code', 'is_available'];

    /**
     * The attributes that should be cast to native types.
     *
     * @var array
     */
    protected $casts = [
        'is_available' => 'boolean',
        'code' => SoftwareCode::class,
    ];

    /**
     *
     * @return array{
     *     start: Software,
     *     attendance: Software,
     *     visitors: Software,
     *     drive: Software
     * }
     *
     */
    public static function list(): array
    {
        return [
            'start' => Software::firstWhere('code', SoftwareCode::Start),
            'attendance' => Software::firstWhere('code', SoftwareCode::Attendance),
            'visitors' => Software::firstWhere('code', SoftwareCode::Visitors),
            'drive' => Software::firstWhere('code', SoftwareCode::Drive),
        ];
    }

    public function softwarePackages()
    {
        return $this->hasMany(SoftwarePackage::class);
    }

    public function roles(): HasMany
    {
        return $this->hasMany(Role::class);
    }
}
