<?php

namespace App\Models;

use App\Enums\SoftwareCode;
use App\Enums\SoftwarePackageCode;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use OwenIt\Auditing\Auditable;
use OwenIt\Auditing\Contracts\Auditable as AuditableContract;

class SoftwarePackage extends Model implements AuditableContract
{
    use Auditable, HasFactory, HasUuids;

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = ['name', 'code', 'software_id', 'is_active'];

    /**
     * The attributes that should be cast to native types.
     *
     * @var array
     */
    protected $casts = [
        'is_active' => 'boolean',
        'code' => SoftwarePackageCode::class,
    ];

    /**
     *
     * @return array{
     *     start: Collection<SoftwarePackage>,
     *     attendance: Collection<SoftwarePackage>,
     *     visitors: Collection<SoftwarePackage>,
     *     drive: Collection<SoftwarePackage>
     * }
     *
     */
    public static function list(): array
    {
        // prettier-ignore to avoid line break
        return [
            'start' => SoftwarePackage::whereRelation('software', 'code', SoftwareCode::Start)->get(),
            'attendance' => SoftwarePackage::whereRelation('software', 'code', SoftwareCode::Attendance)->get(),
            'visitors' => SoftwarePackage::whereRelation('software', 'code', SoftwareCode::Visitors)->get(),
            'drive' => SoftwarePackage::whereRelation('software', 'code', SoftwareCode::Drive)->get(),
        ];
    }

    public function software(): BelongsTo
    {
        return $this->belongsTo(Software::class);
    }

    public function softwareFeatures(): HasMany
    {
        return $this->hasMany(SoftwareFeature::class);
    }
}
