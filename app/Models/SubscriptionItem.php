<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use OwenIt\Auditing\Auditable;
use OwenIt\Auditing\Contracts\Auditable as AuditableContract;

class SubscriptionItem extends Model implements AuditableContract
{
    use Auditable, HasFactory, HasUuids;

    protected static function boot()
    {
        parent::boot();

        static::creating(function ($model) {
            $model->tenant_id = Subscription::find($model['subscription_id'])->tenant_id;
        });
    }

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = ['subscription_id', 'software_id', 'software_package_id', 'tenant_id'];

    public function subscription(): BelongsTo
    {
        return $this->belongsTo(Subscription::class);
    }

    public function tenant(): BelongsTo
    {
        return $this->belongsTo(Tenant::class);
    }

    public function software(): BelongsTo
    {
        return $this->belongsTo(Software::class);
    }

    public function softwarePackage(): BelongsTo
    {
        return $this->belongsTo(SoftwarePackage::class);
    }
}
