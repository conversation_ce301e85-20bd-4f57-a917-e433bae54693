<?php

namespace App\Models;

use App\Enums\Folder;
use App\Enums\SupportTicketSource;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use OwenIt\Auditing\Auditable;
use OwenIt\Auditing\Contracts\Auditable as AuditableContract;

class SupportTicket extends Model implements AuditableContract
{
    use HasFactory;
    use Auditable;

    protected $fillable = [
        'title',
        'description',
        'attachment',
        'source',
        'tenant_id',
        'employee_id',
    ];

    protected $casts = [
        'source' => SupportTicketSource::class,
    ];

    protected function attachmentUrl(): Attribute
    {
        return Attribute::get(
            fn() => Folder::SUPPORT_TICKET_ATTACHMENT->temporaryUrl($this->attachment)
        );
    }

    public function tenant(): BelongsTo
    {
        return $this->belongsTo(Tenant::class);
    }

    public function employee(): <PERSON>ongsTo
    {
        return $this->belongsTo(Employee::class);
    }
}
