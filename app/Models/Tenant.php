<?php

namespace App\Models;

use App\Enums\Folder;
use App\Interfaces\Authenticators\BaseAuthenticatorInterface;
use App\Interfaces\Authenticators\ExternalAuthenticatorInterface;
use Illuminate\Contracts\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Support\Collection;
use Laravel\Sanctum\HasApiTokens;
use Laravel\Sanctum\PersonalAccessToken;
use OwenIt\Auditing\Auditable;
use OwenIt\Auditing\Contracts\Auditable as AuditableContract;
use Storage;

class Tenant extends Authenticatable implements AuditableContract
{
    use Auditable, HasApiTokens, HasFactory, HasUuids;

    protected $fillable = [
        'name',
        'color',
        'is_active',
        'owner_id',
        'allow_sms',
        'colored_logo',
        'white_logo',
        'colored_logo_url',
        'white_logo_url',
    ];

    public function refreshLogos(): void
    {
        $urlExpire = now()->addDays(5);

        $this->update([
            'colored_logo_url' => $this->colored_logo
                ? Storage::temporaryUrl(Folder::LOGOS->path($this->colored_logo), $urlExpire)
                : null,
            'white_logo_url' => $this->white_logo
                ? Storage::temporaryUrl(Folder::LOGOS->path($this->white_logo), $urlExpire)
                : null,
        ]);
    }

    public function rolesBySubscription(): Collection
    {
        $activeSubscription = $this->activeSubscription()
            ->with('subscriptionItems.software.roles')
            ->first();

        if (!$activeSubscription) {
            return collect();
        }

        return $activeSubscription->subscriptionItems
            ->flatMap(
                fn(SubscriptionItem $subscriptionItem) => $subscriptionItem->software->roles->map(
                    fn(Role $role) => $role->setRelation('software', $subscriptionItem->software)
                )
            )
            ->unique(fn(Role $role) => $role->name);
    }

    public function authenticator(): ?BaseAuthenticatorInterface
    {
        return $this->authenticatorConfig
            ? new $this->authenticatorConfig->authenticator_class()
            : null;
    }

    public function hasExternalAuthenticator(): bool
    {
        return $this->authenticator() instanceof ExternalAuthenticatorInterface;
    }

    public function hasInternalAuthenticator(): bool
    {
        return !$this->hasExternalAuthenticator();
    }

    public function scopeActive(Builder $query): void
    {
        $query->where('is_active', true);
    }

    public function owner(): BelongsTo
    {
        return $this->belongsTo(Employee::class);
    }

    public function employees()
    {
        return $this->hasMany(Employee::class);
    }

    public function otps(): HasMany
    {
        return $this->hasMany(Otp::class, 'tenant_id');
    }

    public function departments()
    {
        return $this->hasMany(Department::class);
    }

    public function tags()
    {
        return $this->hasMany(Tag::class);
    }

    public function subscriptions()
    {
        return $this->hasMany(Subscription::class);
    }

    public function activeSubscription()
    {
        return $this->subscriptions()->one()->latestOfMany();
    }

    public function subscriptionItems(): HasMany
    {
        return $this->hasMany(SubscriptionItem::class);
    }

    public function authenticatorConfig()
    {
        return $this->hasOne(TenantAuthenticatorConfig::class)->latest();
    }

    public function personalAccessTokens()
    {
        return $this->morphMany(PersonalAccessToken::class, 'tokenable');
    }

    public function supportTickets(): HasMany
    {
        return $this->hasMany(SupportTicket::class);
    }
}
