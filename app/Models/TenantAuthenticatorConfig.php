<?php

namespace App\Models;

use App\Traits\HasSamlConfig;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use OwenIt\Auditing\Auditable;
use OwenIt\Auditing\Contracts\Auditable as AuditableContract;

class TenantAuthenticatorConfig extends Model implements AuditableContract
{
    use Auditable, HasFactory, HasSamlConfig, HasUuids, SoftDeletes;

    /**
     * The primary key associated with the table.
     *
     * @var string
     */
    protected $primaryKey = 'id';

    protected $guarded = ['id'];

    protected $casts = [
        'config' => 'encrypted:collection',
    ];
}
