<?php

namespace App\Nova;

use App\Models\Passport\Client as ClientModel;
use Illuminate\Http\Request;
use <PERSON>vel\Nova\Fields\ID;
use <PERSON>vel\Nova\Fields\Text;

class Client extends ReadOnlyResource
{
    public static $model = ClientModel::class;

    public static function label()
    {
        return 'OAuth Clients';
    }

    public static $title = 'name';

    public static $search = ['id', 'name', 'redirect'];

    public function fields(Request $request): array
    {
        return [
            ID::make()->sortable(),

            Text::make('User Id')->rules('nullable'),

            Text::make('Name')->rules('required'),

            //            Text::make('Secret')->rules('nullable'),

            Text::make('Provider')->rules('nullable'),

            Text::make('Redirect')->rules('required'),

            Text::make('Logout Url')->rules('nullable'),
        ];
    }

    public function cards(Request $request): array
    {
        return [];
    }

    public function filters(Request $request): array
    {
        return [];
    }

    public function lenses(Request $request): array
    {
        return [];
    }

    public function actions(Request $request): array
    {
        return [];
    }

    public static function authorizedToCreate(Request $request)
    {
        return $request->user()?->isSuperAdmin();
    }

    public function authorizedToUpdate(Request $request)
    {
        return $request->user()?->isSuperAdmin();
    }
}
