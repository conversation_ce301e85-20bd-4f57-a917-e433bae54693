<?php

namespace App\Nova;

use <PERSON>vel\Nova\Fields\BelongsTo;
use Laravel\Nova\Fields\FormData;
use <PERSON>vel\Nova\Fields\ID;
use <PERSON>vel\Nova\Fields\Text;
use Laravel\Nova\Http\Requests\NovaRequest;

class Department extends ReadOnlyResource
{
    public static $model = \App\Models\Department::class;

    public static $title = 'name';

    public static $search = ['id', 'name'];

    public function fields(NovaRequest $request)
    {
        return [
            ID::make()->sortable(),

            Text::make('name'),

            BelongsTo::make('tenant')->readonly(
                fn($request) => $request->isUpdateOrUpdateAttachedRequest()
            ),

            BelongsTo::make('parent', 'parent', Department::class)
                ->nullable()
                ->dependsOn(['tenant'], function (
                    BelongsTo $filed,
                    NovaRequest $request,
                    FormData $formData
                ) {
                    if (!$formData->tenant) {
                        $filed->hide();
                    }
                    $filed->relatableQueryUsing(
                        fn($request, $query) => $query->where('tenant_id', $formData->tenant)
                    );
                }),

            BelongsTo::make('manager', 'manager', Employee::class)
                ->displayUsing(fn($employee) => $employee->first_name . ' ' . $employee->last_name)
                ->dependsOn(['tenant'], function (
                    BelongsTo $filed,
                    NovaRequest $request,
                    FormData $formData
                ) {
                    if (!$formData->tenant) {
                        $filed->hide();
                    }
                    $filed->relatableQueryUsing(
                        fn($request, $query) => $query->where('tenant_id', $formData->tenant)
                    );
                }),
        ];
    }

    public function cards(NovaRequest $request)
    {
        return [];
    }

    public function filters(NovaRequest $request)
    {
        return [];
    }

    public function lenses(NovaRequest $request)
    {
        return [];
    }

    public function actions(NovaRequest $request)
    {
        return [];
    }
}
