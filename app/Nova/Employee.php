<?php

namespace App\Nova;

use App\Enums\EmployeePreferredLanguage;
use Illuminate\Http\Request;
use <PERSON>vel\Nova\Fields\BelongsTo;
use <PERSON>vel\Nova\Fields\Boolean;
use <PERSON>vel\Nova\Fields\Email;
use <PERSON><PERSON>\Nova\Fields\FormData;
use <PERSON>vel\Nova\Fields\ID;
use <PERSON>vel\Nova\Fields\Select;
use Laravel\Nova\Fields\Tag;
use Laravel\Nova\Fields\Text;
use Laravel\Nova\Http\Requests\NovaRequest;

class Employee extends ReadOnlyResource
{
    public static $model = \App\Models\Employee::class;

    public static $title = 'first_name';

    public static $search = ['id', 'first_name', 'last_name', 'email', 'tenant.id'];

    public function fields(NovaRequest $request)
    {
        return [
            ID::make()->sortable(),

            Text::make('first_name')->rules(['required']),

            Text::make('last_name'),

            Email::make('email')->rules(['required']),

            Tag::make('Roles', 'roles', Role::class)->searchable(),

            Text::make('number')->rules(['required']),

            Text::make('phone'),

            Boolean::make('is_active'),

            BelongsTo::make('tenant')
                ->readonly(fn($request) => $request->isUpdateOrUpdateAttachedRequest())
                ->searchable()
                ->creationRules('required'),

            Select::make('preferred_language')
                ->options([
                    'ar' => EmployeePreferredLanguage::Arabic,
                    'en' => EmployeePreferredLanguage::English,
                ])
                ->rules(['required']),

            BelongsTo::make('department')
                ->nullable()
                ->dependsOn(['tenant'], function (
                    BelongsTo $filed,
                    NovaRequest $request,
                    FormData $formData
                ) {
                    if (!$formData->tenant) {
                        $filed->hide();
                    }
                    $filed->relatableQueryUsing(
                        fn($request, $query) => $query->where('tenant_id', $formData->tenant)
                    );
                }),

            Text::make('position'),
        ];
    }

    public function cards(NovaRequest $request)
    {
        return [];
    }

    public function filters(NovaRequest $request)
    {
        return [];
    }

    public function lenses(NovaRequest $request)
    {
        return [];
    }

    public function actions(NovaRequest $request)
    {
        return [];
    }

    public static function authorizedToCreate(Request $request)
    {
        return true;
    }

    /**
     * Handle any post-creation validation processing.
     *
     * @param  \Laravel\Nova\Http\Requests\NovaRequest  $request
     * @param  \Illuminate\Validation\Validator  $validator
     * @return void
     */
    protected static function afterCreationValidation(NovaRequest $request, $validator)
    {
        if (
            \App\Models\Tenant::find($request->tenant)
                ->employees()
                ->exists()
        ) {
            $validator
                ->errors()
                ->add('tenant', 'Can not add employees when tenant already has employees');
        }
    }
}
