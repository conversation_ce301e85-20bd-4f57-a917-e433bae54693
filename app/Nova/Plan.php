<?php

namespace App\Nova;

use App\Models\Plan as PlanModel;
use Illuminate\Http\Request;
use <PERSON><PERSON>\Nova\Fields\BelongsTo;
use <PERSON><PERSON>\Nova\Fields\HasMany;
use <PERSON><PERSON>\Nova\Fields\ID;
use <PERSON>vel\Nova\Fields\Number;
use <PERSON><PERSON>\Nova\Fields\Text;

class Plan extends Resource
{
    public static $model = PlanModel::class;

    public static $title = 'name';

    public static $search = ['id', 'name'];

    public function fields(Request $request): array
    {
        return [
            ID::make(),

            Text::make('Name')->rules('required'),

            Number::make('Price')->rules('required', 'numeric'),

            Number::make('Is Custom')->rules('required', 'integer'),

            BelongsTo::make('CustomizedForTenant', 'customizedForTenant', Tenant::class),

            HasMany::make('PlanItems', 'planItems'),
        ];
    }

    public function cards(Request $request): array
    {
        return [];
    }

    public function filters(Request $request): array
    {
        return [];
    }

    public function lenses(Request $request): array
    {
        return [];
    }

    public function actions(Request $request): array
    {
        return [];
    }
}
