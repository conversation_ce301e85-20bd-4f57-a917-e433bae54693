<?php

namespace App\Nova;

use Illuminate\Http\Request;

abstract class ReadOnlyResource extends Resource
{
    public function authorizeToView(Request $request)
    {
        return true;
    }

    public function authorizedToView(Request $request)
    {
        return true;
    }

    public static function authorizedToViewAny(Request $request)
    {
        return true;
    }

    public static function authorizedToCreate(Request $request)
    {
        return false;
    }

    public function authorizedToUpdate(Request $request)
    {
        return false;
    }

    public function authorizedToDelete(Request $request)
    {
        return false;
    }

    public function authorizedToRestore(Request $request)
    {
        return false;
    }

    public function authorizedToForceDelete(Request $request)
    {
        return false;
    }

    public function authorizedToAdd(Request $request, $model)
    {
        return true;
    }

    // public function authorizedToAttach(Request $request, $model)
    // {
    //     return true;
    // }

    // public function authorizedToAttachAny(Request $request, $model)
    // {
    //     return true;
    // }

    public function authorizedToReplicate(Request $request)
    {
        return false;
    }
}
