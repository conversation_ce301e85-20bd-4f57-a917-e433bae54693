<?php

namespace App\Nova;

use Illuminate\Http\Request;
use <PERSON><PERSON>\Nova\Fields\Boolean;
use <PERSON><PERSON>\Nova\Fields\HasMany;
use <PERSON><PERSON>\Nova\Fields\ID;
use <PERSON>vel\Nova\Fields\Text;

class Software extends ReadOnlyResource
{
    public static $model = \App\Models\Software::class;

    public static $title = 'name';

    public static $search = ['id', 'name', 'code'];

    public function fields(Request $request): array
    {
        return [
            ID::make(),

            Text::make('Name')->rules('required'),

            Text::make('Code')->rules('required'),

            Boolean::make('is_available'),

            HasMany::make('Software Packages', 'softwarePackages'),

            HasMany::make('Roles', 'roles'),
        ];
    }

    public function cards(Request $request): array
    {
        return [];
    }

    public function filters(Request $request): array
    {
        return [];
    }

    public function lenses(Request $request): array
    {
        return [];
    }

    public function actions(Request $request): array
    {
        return [];
    }
}
