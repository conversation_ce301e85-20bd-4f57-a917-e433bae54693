<?php

namespace App\Nova;

use Illuminate\Http\Request;
use <PERSON>vel\Nova\Fields\BelongsTo;
use <PERSON>vel\Nova\Fields\ID;
use <PERSON><PERSON>\Nova\Fields\Text;

class SoftwareFeature extends ReadOnlyResource
{
    public static $model = \App\Models\SoftwareFeature::class;

    public static $title = 'code';

    public static $search = ['id', 'code', 'software_package_id'];

    public function fields(Request $request): array
    {
        return [
            ID::make(),

            Text::make('Code')->rules('required'),

            BelongsTo::make('SoftwarePackage', 'softwarePackage', SoftwarePackage::class),
        ];
    }

    public function cards(Request $request): array
    {
        return [];
    }

    public function filters(Request $request): array
    {
        return [];
    }

    public function lenses(Request $request): array
    {
        return [];
    }

    public function actions(Request $request): array
    {
        return [];
    }
}
