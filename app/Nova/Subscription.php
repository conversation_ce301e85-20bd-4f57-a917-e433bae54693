<?php

namespace App\Nova;

use App\Models\Subscription as SubscriptionModel;
use Illuminate\Http\Request;
use <PERSON><PERSON>\Nova\Fields\BelongsTo;
use <PERSON><PERSON>\Nova\Fields\Date;
use <PERSON>vel\Nova\Fields\HasMany;
use <PERSON>vel\Nova\Fields\ID;
use <PERSON><PERSON>\Nova\Fields\Number;

class Subscription extends Resource
{
    public static $model = SubscriptionModel::class;

    public static $title = 'id';

    public static $search = ['id', 'tenant_id', 'plan_id'];

    public function fields(Request $request): array
    {
        return [
            ID::make(),

            Number::make('Total Employees')->rules('required', 'integer'),

            Date::make('Start At')->rules('required', 'date'),

            Date::make('End At')->rules('required', 'date'),

            Number::make('Price')->rules('required', 'numeric'),

            HasMany::make('SubscriptionItems', 'subscriptionItems'),

            BelongsTo::make('Plan', 'plan', Plan::class)->nullable(),

            BelongsTo::make('Tenant', 'tenant', Tenant::class),
        ];
    }

    public function cards(Request $request): array
    {
        return [];
    }

    public function filters(Request $request): array
    {
        return [];
    }

    public function lenses(Request $request): array
    {
        return [];
    }

    public function actions(Request $request): array
    {
        return [];
    }
}
