<?php

namespace App\Nova;

use App\Models\SupportTicket as SupportTicketModel;
use Illuminate\Http\Request;
use <PERSON><PERSON>\Nova\Fields\BelongsTo;
use <PERSON>vel\Nova\Fields\File;
use <PERSON>vel\Nova\Fields\ID;
use <PERSON>vel\Nova\Fields\Text;

class SupportTicket extends ReadOnlyResource
{
    public static $model = SupportTicketModel::class;

    public static $title = 'title';

    public static $search = ['id', 'title', 'description', 'source'];

    public function fields(Request $request): array
    {
        return [
            ID::make()->sortable(),

            Text::make('Title')->sortable(),

            Text::make('Description')->sortable(),

            File::make('Attachment', 'attachment_url'),
            Text::make('Source')->sortable(),

            BelongsTo::make('Tenant', 'tenant', Tenant::class),

            BelongsTo::make('Employee', 'employee', Employee::class),
        ];
    }

    public function cards(Request $request): array
    {
        return [];
    }

    public function filters(Request $request): array
    {
        return [];
    }

    public function lenses(Request $request): array
    {
        return [];
    }

    public function actions(Request $request): array
    {
        return [];
    }
}
