<?php

namespace App\Nova;

use Illuminate\Support\Facades\Validator;
use <PERSON>vel\Nova\Fields\ID;
use <PERSON>vel\Nova\Fields\KeyValue;
use <PERSON>vel\Nova\Fields\Select;
use <PERSON>vel\Nova\Http\Requests\NovaRequest;

class TenantAuthenticatorConfig extends Resource
{
    public static $model = \App\Models\TenantAuthenticatorConfig::class;

    public static $title = 'id';

    public static $search = ['id', 'tenant_id', 'tenant.domain'];

    public function fields(NovaRequest $request)
    {
        return [
            ID::make()->sortable(),
            Select::make('Authenticator Type', 'authenticator_class')
                ->searchable()
                ->options([
                    \App\Authenticators\SamlAuthenticator::class => 'SAML',
                ])
                ->displayUsingLabels(),
            KeyValue::make('config')
                ->rules(['json'])
                ->help(
                    view('partials.authenticator-config-help-text', [
                        'name' => $this->name,
                    ])->render()
                ),
        ];
    }

    protected static function afterValidation(NovaRequest $request, $validator)
    {
        $request->merge(['array_config' => json_decode($request->config, true)]);

        $customValidator = Validator::make($request->all(), [
            'array_config.idp_entity_id' =>
                'required_if:authenticator_class,' . \App\Authenticators\SamlAuthenticator::class,
            'array_config.idp_login_url' =>
                'required_if:authenticator_class,' . \App\Authenticators\SamlAuthenticator::class,
            'array_config.name_id_format' =>
                'required_if:authenticator_class,' . \App\Authenticators\SamlAuthenticator::class,
            'array_config.idp_x509_cert' =>
                'required_if:authenticator_class,' . \App\Authenticators\SamlAuthenticator::class,
            'array_config.email_claim' =>
                'required_if:authenticator_class,' . \App\Authenticators\SamlAuthenticator::class,
            'array_config.first_name_claim' =>
                'required_if:authenticator_class,' . \App\Authenticators\SamlAuthenticator::class,
            'array_config.employee_number_claim' =>
                'required_if:authenticator_class,' . \App\Authenticators\SamlAuthenticator::class,
        ]);

        if ($customValidator->fails()) {
            foreach ($customValidator->errors()->messages() as $error) {
                $validator->errors()->add('config', $error[0]);
            }
        }
    }

    public function cards(NovaRequest $request)
    {
        return [];
    }

    public function filters(NovaRequest $request)
    {
        return [];
    }

    public function lenses(NovaRequest $request)
    {
        return [];
    }

    public function actions(NovaRequest $request)
    {
        return [];
    }
}
