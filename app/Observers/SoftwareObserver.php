<?php

namespace App\Observers;

use App\Enums\SyncName;
use App\Messages\SyncMessage;
use App\Models\Software;
use Illuminate\Contracts\Events\ShouldHandleEventsAfterCommit;

class SoftwareObserver implements ShouldHandleEventsAfterCommit
{
    /**
     * Handle the Software "created" event.
     */
    public function created(Software $software): void
    {
        (new SyncMessage($this->format($software), SyncName::SoftwareCreate))->send();
    }

    /**
     * Handle the Software "updated" event.
     */
    public function updated(Software $software): void
    {
        (new SyncMessage($this->format($software), SyncName::SoftwareUpdate))->send();
    }

    /**
     * Handle the Software "deleted" event.
     */
    public function deleted(Software $software): void
    {
        (new SyncMessage($this->format($software), SyncName::SoftwareDelete))->send();
    }

    public function format(Software $software): array
    {
        return $software->refresh()->load('softwarePackages.softwareFeatures')->toArray();
    }
}
