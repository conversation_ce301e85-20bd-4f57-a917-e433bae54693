<?php

namespace App\Observers;

use App\Enums\SyncName;
use App\Messages\SyncMessage;
use App\Models\SubscriptionItem;
use Illuminate\Contracts\Events\ShouldHandleEventsAfterCommit;

class SubscriptionItemObserver implements ShouldHandleEventsAfterCommit
{
    /**
     * Handle the SubscriptionItem "created" event.
     */
    public function created(SubscriptionItem $subscriptionItem): void
    {
        (new SyncMessage(
            $this->format($subscriptionItem),
            SyncName::SubscriptionItemCreate
        ))->send();
    }

    /**
     * Handle the SubscriptionItem "updated" event.
     */
    public function updated(SubscriptionItem $subscriptionItem): void
    {
        (new SyncMessage(
            $this->format($subscriptionItem),
            SyncName::SubscriptionItemUpdate
        ))->send();
    }

    /**
     * Handle the SubscriptionItem "deleted" event.
     */
    public function deleted(SubscriptionItem $subscriptionItem): void
    {
        (new SyncMessage(
            $this->format($subscriptionItem),
            SyncName::SubscriptionItemDelete
        ))->send();
    }

    public function format(SubscriptionItem $subscriptionItem): array
    {
        return $subscriptionItem
            ->refresh()
            ->load(['softwarePackage', 'software'])
            ->toArray();
    }
}
