<?php

namespace App\Observers;

use App\Enums\SyncName;
use App\Messages\SyncMessage;
use App\Models\Subscription;
use Illuminate\Contracts\Events\ShouldHandleEventsAfterCommit;

class SubscriptionObserver implements ShouldHandleEventsAfterCommit
{
    /**
     * Handle the Subscription "created" event.
     */
    public function created(Subscription $subscription): void
    {
        (new SyncMessage($this->format($subscription), SyncName::SubscriptionCreate))->send();
    }

    /**
     * Handle the Subscription "updated" event.
     */
    public function updated(Subscription $subscription): void
    {
        (new SyncMessage($this->format($subscription), SyncName::SubscriptionUpdate))->send();
    }

    /**
     * Handle the Subscription "deleted" event.
     */
    public function deleted(Subscription $subscription): void
    {
        (new SyncMessage($this->format($subscription), SyncName::SubscriptionDelete))->send();
    }

    public function format(Subscription $subscription): array
    {
        return $subscription->refresh()->load('subscriptionItems')->toArray();
    }
}
