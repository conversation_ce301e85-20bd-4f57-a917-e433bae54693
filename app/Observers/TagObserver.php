<?php

namespace App\Observers;

use App\Enums\SyncName;
use App\Messages\SyncMessage;
use App\Models\Tag;
use Illuminate\Contracts\Events\ShouldHandleEventsAfterCommit;

class TagObserver implements ShouldHandleEventsAfterCommit
{
    /**
     * Handle the Tag "created" event.
     */
    public function created(Tag $tag): void
    {
        (new SyncMessage($this->format($tag), SyncName::TagCreate))->send();
    }

    /**
     * Handle the Tag "updated" event.
     */
    public function updated(Tag $tag): void
    {
        (new SyncMessage($this->format($tag), SyncName::TagUpdate))->send();
    }

    /**
     * Handle the Tag "deleted" event.
     */
    public function deleted(Tag $tag): void
    {
        (new SyncMessage($this->format($tag), SyncName::TagDelete))->send();
    }

    public function format(Tag $tag): array
    {
        $tag->refresh();

        return [
            'id' => $tag->id,
            'tenant_id' => $tag->tenant_id,
            'name' => $tag->name,
            'color' => $tag->color,
        ];
    }
}
