<?php

namespace App\Providers;

use App\Models\Admin as AdminModel;
use App\Nova\Admin;
use App\Nova\Client;
use App\Nova\Dashboards\Main;
use App\Nova\Department;
use App\Nova\Employee;
use App\Nova\Plan;
use App\Nova\PlanItem;
use App\Nova\Role;
use App\Nova\Software;
use App\Nova\SoftwareFeature;
use App\Nova\SoftwarePackage;
use App\Nova\Subscription;
use App\Nova\SubscriptionItem;
use App\Nova\SupportTicket;
use App\Nova\Tenant;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Gate;
use Laravel\Nova\Menu\MenuItem;
use Laravel\Nova\Menu\MenuSection;
use Laravel\Nova\Nova;
use Laravel\Nova\NovaApplicationServiceProvider;
use function auth;

class NovaServiceProvider extends NovaApplicationServiceProvider
{
    /**
     * Bootstrap any application services.
     *
     * @return void
     */
    public function boot()
    {
        parent::boot();

        // force the locale to be English
        Nova::booted(fn() => app()->setLocale('en'));

        Nova::mainMenu(function (Request $request) {
            return [
                MenuSection::dashboard(Main::class)->icon('chart-bar'),

                MenuSection::make('Tenant', [
                    MenuItem::resource(Tenant::class),
                    MenuItem::resource(Department::class),
                    MenuItem::resource(Employee::class),
                ])
                    ->icon('template')
                    ->collapsable(),

                MenuSection::make('Software', [
                    MenuItem::resource(Software::class),
                    MenuItem::resource(SoftwarePackage::class),
                    MenuItem::resource(SoftwareFeature::class),
                ])
                    ->icon('template')
                    ->collapsable(),

                MenuSection::make('Plan', [
                    MenuItem::resource(Plan::class),
                    MenuItem::resource(PlanItem::class),
                ])
                    ->icon('template')
                    ->collapsable(),

                MenuSection::make('Subscription', [
                    MenuItem::resource(Subscription::class),
                    MenuItem::resource(SubscriptionItem::class),
                ])
                    ->icon('template')
                    ->collapsable(),

                MenuSection::make('Administration', [
                    MenuItem::resource(Admin::class),
                    MenuItem::resource(Role::class),
                    MenuItem::resource(SupportTicket::class),
                    MenuItem::resource(Client::class),
                ])
                    ->icon('user-circle')
                    ->collapsable(),
            ];
        });
    }

    /**
     * Register the Nova routes.
     *
     * @return void
     */
    protected function routes()
    {
        Nova::routes()->withAuthenticationRoutes()->withPasswordResetRoutes()->register();
    }

    /**
     * Register the Nova gate.
     *
     * This gate determines who can access Nova in non-local environments.
     *
     * @return void
     */
    protected function gate()
    {
        Gate::define('viewNova', fn(?AdminModel $user) => auth('admin')->check());
    }

    /**
     * Get the dashboards that should be listed in the Nova sidebar.
     *
     * @return array
     */
    protected function dashboards()
    {
        return [new Main()];
    }

    /**
     * Get the tools that should be listed in the Nova sidebar.
     *
     * @return array
     */
    public function tools()
    {
        return [];
    }

    /**
     * Register any application services.
     *
     * @return void
     */
    public function register()
    {
        //
    }
}
