<?php

namespace App\Providers;

use App\Models\Employee;
use Illuminate\Cache\RateLimiting\Limit;
use Illuminate\Foundation\Support\Providers\RouteServiceProvider as ServiceProvider;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\RateLimiter;
use Illuminate\Support\Facades\Route;
use Illuminate\Support\Facades\URL;

class RouteServiceProvider extends ServiceProvider
{
    /**
     * Define your route model bindings, pattern filters, and other route configuration.
     */
    public function boot(): void
    {
        URL::forceScheme('https');

        RateLimiter::for('api', function (Request $request) {
            return Limit::perMinute(60)->by($request->user()?->id ?: $request->ip());
        });

        $this->routes(function () {
            Route::get('up', function () {
                Employee::exists();

                return response('');
            });

            Route::middleware(['api', 'auth:api', 'localization', 'set-sentry-user'])->group(
                function () {
                    Route::prefix('api/v1/tenant')->group(base_path('routes/api.php'));

                    Route::prefix('api/v1')->group(base_path('routes/common.php'));
                }
            );

            Route::middleware(['web', 'throttle:60'])->group(base_path('routes/auth.php'));

            Route::middleware('saml')->group(base_path('routes/saml.php'));

            Route::middleware(['auth:sanctum', 'localization', 'active-tenant'])
                ->prefix('api')
                ->group(base_path('routes/v1/external.php'));
        });
    }
}
