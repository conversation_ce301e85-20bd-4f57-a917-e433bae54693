<?php

namespace App\Providers;

use App\Models\Employee;
use App\Models\Role;
use App\Models\Tenant;
use Illuminate\Http\Request;
use Illuminate\Support\ServiceProvider;
use Knuckles\Scribe\Scribe;

/**
 * @codeCoverageIgnore
 */
class ScribeServiceProvider extends ServiceProvider
{
    public function boot(): void
    {
        if (!class_exists(Scribe::class)) {
            return;
        }

        Scribe::bootstrap(function () {
            app()->setLocale('en');
        });

        Scribe::beforeResponseCall(function (Request $request) {
            // We need to authenticate using JWT
            $employee =
                Employee::firstWhere('email', '<EMAIL>') ??
                Employee::factory()
                    ->for(Tenant::factory())
                    ->create([
                        'email' => '<EMAIL>',
                    ]);

            $employee->assignRole(Role::all());

            $token = $employee->createToken('test')->accessToken;

            $request->headers->set('Authorization', "Bearer $token");

            // You also need to set the headers in $_SERVER
            $request->server->set('HTTP_AUTHORIZATION', "Bearer $token");
        });
    }
}
