<?php

namespace App\QueryBuilders;

use <PERSON><PERSON>\QueryBuilder\AllowedFilter;
use <PERSON><PERSON>\QueryBuilder\QueryBuilder;
use function currentTenant;

class EmployeeQueryBuilder
{
    public static function query(): QueryBuilder
    {
        return QueryBuilder::for(currentTenant()->employees())
            ->allowedFilters([
                AllowedFilter::exact('department_id'),
                AllowedFilter::exact('is_active'),
                AllowedFilter::exact('employees', 'id'),
                AllowedFilter::exact('direct_managers', 'manager_id'),
                AllowedFilter::callback(
                    'is_direct_manager',
                    fn($query, bool $isDirectManager) => $isDirectManager
                        ? $query->has('directEmployees')
                        : $query->doesntHave('directEmployees')
                ),
                AllowedFilter::callback('search', fn($q, $value) => $q->search($value)),
                AllowedFilter::callback('departments', fn($q, $l) => $q->filterByDepartments($l)),
                AllowedFilter::callback('tags', fn($query, $tags) => $query->filterByTags($tags)),
                AllowedFilter::callback(
                    'exclude',
                    fn($query, $value) => $query->where('id', '!=', $value)
                ),
            ])
            ->allowedSorts(['updated_at', 'created_at'])
            ->defaultSort(['-is_active', 'created_at'])
            ->allowedIncludes(['department', 'tags', 'directManager'])
            ->orderBy('is_active', 'desc');
    }
}
