<?php

namespace App\Resolvers;

use App\Enums\AuditAgent;
use OwenIt\Auditing\Contracts\Auditable;
use OwenIt\Auditing\Contracts\Resolver;

class AgentResolver implements Resolver
{
    public static function resolve(Auditable $auditable): string
    {
        if (app()->runningInConsole()) {
            return AuditAgent::CONSOLE->value;
        }

        if (request()->is('api/mobile/*', 'api/*/mobile/*')) {
            return AuditAgent::MOBILE->value;
        }

        if (request()->is('api/*/external/*')) {
            return AuditAgent::EXTERNAL->value;
        }

        if (request()->is('nova-api/*', 'nova/*')) {
            return AuditAgent::NOVA->value;
        }

        if (request()->is('api/*/tenant/*')) {
            return AuditAgent::FRONTEND->value;
        }

        return AuditAgent::WEB->value;
    }
}
