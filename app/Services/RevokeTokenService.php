<?php

namespace App\Services;

use Http;
use <PERSON><PERSON>\Passport\RefreshTokenRepository;
use Lara<PERSON>\Passport\Token;

class RevokeTokenService
{
    public function __construct(private Token $token, private ?string $browserId = null)
    {
    }

    public function handle(): void
    {
        if ($this->token->client->logout_url && $this->browserId) {
            Http::retry(5, 1000)
                ->timeout(30)
                ->delete($this->token->client->logout_url, [
                    'browser_id' => $this->browserId,
                ]);
        }

        $this->token->revoke();

        (new RefreshTokenRepository())->revokeRefreshTokensByAccessTokenId($this->token->getKey());
    }
}
