<?php

namespace App\Services\Saml;

use App\Exceptions\InvalidSamlDestination;
use App\Exceptions\InvalidSamlIssuer;
use App\Exceptions\InvalidSamlSignature;
use App\Models\TenantAuthenticatorConfig;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use LightSaml\Context\Profile\MessageContext;
use LightSaml\Credential\KeyHelper;
use LightSaml\Credential\X509Certificate;
use LightSaml\Model\Assertion\Assertion;
use LightSaml\Model\Assertion\Attribute;
use LightSaml\Model\Assertion\NameID;
use LightSaml\Model\Context\DeserializationContext;
use LightSaml\Model\Protocol\SamlMessage;
use LightSaml\Validator\Model\Assertion\AssertionTimeValidator;
use RobRichards\XMLSecLibs\XMLSecurityKey;

class SamlReceiveResponseService
{
    private SamlMessage $message;

    private DeserializationContext $deserializationContext;

    private Assertion $assertion;

    /**
     * @throws InvalidSamlSignature
     * @throws InvalidSamlDestination
     */
    public function __construct(
        Request $request,
        private readonly TenantAuthenticatorConfig $tenantAuthenticatorConfig
    ) {
        $bindingFactory = new \LightSaml\Binding\BindingFactory();
        $binding = $bindingFactory->getBindingByRequest($request);

        $messageContext = new MessageContext();
        /** @var \LightSaml\Model\Protocol\Response $response */
        $binding->receive($request, $messageContext);

        $this->message = $messageContext->getMessage();
        $this->deserializationContext = $messageContext->getDeserializationContext();

        $this->validateSignature(); // if signed
        $this->decryptAssertions(); // if encrypted

        $idpEntityId = $this->tenantAuthenticatorConfig->idpEntityId;
        $issuer = $this->message->getIssuer()->getValue();

        $assertionTimeValidator = new AssertionTimeValidator();
        $assertionTimeValidator->validateTimeRestrictions($this->assertion, now()->timestamp, 30);

        if ($issuer != $idpEntityId) {
            throw new InvalidSamlIssuer(
                "The issuer is not right, given: $issuer, should be $idpEntityId"
            );
        }

        // validate the destination
        $destination = strtok($this->message->getDestination(), '?');
        $currentUrl = url()->current();
        if (url()->current() != $destination) {
            throw new InvalidSamlDestination(
                "Current: $currentUrl, It should be the Destination: $destination"
            );
        }
    }

    public static function getCertificate(): X509Certificate
    {
        $certificate = new X509Certificate();
        $certificate->loadPem(config('saml.x509cert'));

        return $certificate;
    }

    public static function getPrivateKey(): XMLSecurityKey
    {
        return KeyHelper::createPrivateKey(config('saml.privateKey'), '');
    }

    /**
     * @throws InvalidSamlSignature
     */
    public function validateSignature(): void
    {
        Log::info('Start Response Signature validation');
        $response = new \LightSaml\Model\Protocol\Response();
        $response->deserialize(
            $this->deserializationContext->getDocument()->firstChild,
            $this->deserializationContext
        );

        $certificate = new X509Certificate();
        $certificate->setData($this->tenantAuthenticatorConfig->idpX509Cert);

        $key = \LightSaml\Credential\KeyHelper::createPublicKey($certificate);

        /** @var \LightSaml\Model\XmlDSig\SignatureXmlReader $signatureReader */
        $signatureReader = $response->getSignature();

        if (is_null($signatureReader)) {
            Log::info('The response was not signed, proceed');

            return;
        }

        $ok = $signatureReader->validate($key);
        if (!$ok) {
            Log::warning('Signature is not valid or could not be validated');
            throw new InvalidSamlSignature('Signature is not valid or could not be validated');
        }
    }

    public function decryptAssertions(): void
    {
        $response = new \LightSaml\Model\Protocol\Response();
        $response->deserialize(
            $this->deserializationContext->getDocument()->firstChild,
            $this->deserializationContext
        );

        // load you key par credential
        $credential = new \LightSaml\Credential\X509Credential(
            self::getCertificate(),
            self::getPrivateKey()
        );

        // decrypt the Assertion with your credential
        $decryptDeserializeContext = new \LightSaml\Model\Context\DeserializationContext();
        /** @var \LightSaml\Model\Assertion\EncryptedAssertionReader $reader */
        $reader = $response->getFirstEncryptedAssertion();
        if (!$reader) {
            // assertions are not encrypted
            return;
        }

        $this->assertion = $reader->decryptMultiAssertion(
            [$credential],
            $decryptDeserializeContext
        );
    }

    public function getAssertion(): Assertion
    {
        return $this->assertion;
    }

    public function getMessage(): SamlMessage
    {
        return $this->message;
    }

    /**
     * @return Attribute[]
     */
    public function getAttributes()
    {
        return $this->assertion->getFirstAttributeStatement()->getAllAttributes();
    }

    public function getNameId(): NameID
    {
        return $this->assertion->getSubject()->getNameID();
    }

    public function getAttribute(?string $name): ?Attribute
    {
        if (is_null($name)) {
            return null;
        }

        return $this->assertion->getFirstAttributeStatement()->getFirstAttributeByName($name);
    }
}
