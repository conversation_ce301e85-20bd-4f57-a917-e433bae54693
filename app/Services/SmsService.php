<?php

namespace App\Services;

use App\DTOs\Identifier;
use Illuminate\Support\Facades\Http;
use InvalidArgumentException;

class SmsService
{
    public static function send(
        Identifier $identifier,
        string $message,
        callable $onSuccess = null
    ): void {
        if (!$identifier->isPhone()) {
            throw new InvalidArgumentException('Only phone identifiers are supported');
        }

        if (!config('services.sms.secret') || !config('services.sms.url') || app()->isLocal()) {
            return;
        }

        Http::retry(5, 1000)
            ->timeout(30)
            ->with<PERSON>eader('x-tamkeen-apikey', config('services.sms.secret'))
            ->post(config('services.sms.url'), [
                'recipient' => $identifier->value(),
                'body' => $message,
            ])
            ->throw();

        if ($onSuccess) {
            $onSuccess();
        }
    }
}
