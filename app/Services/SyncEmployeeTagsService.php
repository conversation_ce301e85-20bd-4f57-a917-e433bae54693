<?php

namespace App\Services;

use App\Models\Employee;
use App\Models\Tag;
use App\Observers\EmployeeObserver;

class SyncEmployeeTagsService
{
    public function __construct(private Employee $employee, private array $tags)
    {
    }

    public function handle(): Employee
    {
        $tagsIds = collect($this->tags)
            ->map(
                fn($tag) => Tag::updateOrCreate(
                    ['name' => $tag['name'], 'tenant_id' => currentTenant()->id],
                    ['color' => $tag['color']]
                )
            )
            ->pluck('id');

        $this->employee->tags()->sync($tagsIds);

        (new EmployeeObserver())->updated($this->employee);

        return $this->employee;
    }
}
