<?php

namespace App\Traits;

use Illuminate\Database\Eloquent\Casts\Attribute;

/**
 * @deprecated plase do not use this
 * @codeCoverageIgnore
 */
trait HasLdapConfig
{
    public function ldapHost(): Attribute
    {
        return Attribute::make(get: fn() => @$this->config['ldap_host']);
    }

    public function ldapPort(): Attribute
    {
        return Attribute::make(get: fn() => @$this->config['ldap_port'] ?: 389);
    }

    public function ldapBaseDN(): Attribute
    {
        return Attribute::make(get: fn() => @$this->config['ldap_base_dn']);
    }

    public function ldapUsername(): Attribute
    {
        return Attribute::make(get: fn() => @$this->config['ldap_username']);
    }

    public function ldapPassword(): Attribute
    {
        return Attribute::make(get: fn() => @$this->config['ldap_password']);
    }

    public function ldapUsernameField(): Attribute
    {
        return Attribute::make(get: fn() => @$this->config['ldap_username_field'] ?: 'cn');
    }

    public function ldapDistinguishednameField(): Attribute
    {
        return Attribute::make(
            get: fn() => @$this->config['ldap_distinguishedname_field'] ?: 'distinguishedname'
        );
    }

    public function ldapFirstNameField(): Attribute
    {
        return Attribute::make(get: fn() => @$this->config['ldap_first_name_field'] ?: 'cn');
    }

    public function ldapLastNameField(): Attribute
    {
        return Attribute::make(get: fn() => @$this->config['ldap_last_name_field'] ?: 'sn');
    }

    public function ldapEmployeeNumberField(): Attribute
    {
        return Attribute::make(get: fn() => @$this->config['ldap_employee_number_field']);
    }

    public function ldapEmployeePositionField(): Attribute
    {
        return Attribute::make(get: fn() => @$this->config['ldap_employee_position_field']);
    }

    public function ldapUseSSL(): Attribute
    {
        return Attribute::make(get: fn() => @$this->config['ldap_use_ssl'] ?: false);
    }

    public function ldapUseTLS(): Attribute
    {
        return Attribute::make(get: fn() => @$this->config['ldap_use_tls'] ?: false);
    }

    public function ldapUseSASL(): Attribute
    {
        return Attribute::make(get: fn() => @$this->config['ldap_use_sasl'] ?: false);
    }
}
