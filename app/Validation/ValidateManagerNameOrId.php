<?php

namespace App\Validation;

use Illuminate\Contracts\Validation\Validator;
use function currentTenant;
use function request;

class ValidateManagerNameOrId
{
    public function __invoke(Validator $validator)
    {
        $email = request('manager_email');
        $id = request('manager_id');

        if (!$id && !$email) {
            return;
        }

        $manager = $email
            ? currentTenant()->employees()->firstWhere('email', $email)
            : currentTenant()->employees()->firstWhere('id', $id);

        if ($manager) {
            request()->merge(['manager_id' => $manager->id]);
            return;
        }

        $validator
            ->errors()
            ->add($email ? 'manager_email' : 'manager_id', 'manager does not exist');
    }
}
