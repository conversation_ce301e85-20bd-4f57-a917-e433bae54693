<?php

namespace App\Validation;

use Illuminate\Contracts\Validation\Validator;
use function currentTenant;
use function request;

class ValidateParentDepartmentNameOrId
{
    public function __invoke(Validator $validator)
    {
        $id = request('parent_id');
        $name = request('parent_name');

        if (!$id && !$name) {
            return;
        }

        $parent = $name
            ? currentTenant()->departments()->firstWhere('name', $name)
            : currentTenant()->departments()->firstWhere('id', $id);

        if ($parent) {
            request()->merge(['parent_id' => $parent->id]);
            return;
        }

        $validator
            ->errors()
            ->add($id ? 'parent_id' : 'parent_name', 'parent department does not exist');
    }
}
