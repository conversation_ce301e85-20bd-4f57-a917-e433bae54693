{"name": "laravel/laravel", "type": "project", "description": "The skeleton application for the Laravel framework.", "keywords": ["laravel", "framework"], "license": "MIT", "require": {"php": "^8.3", "corbosman/laravel-passport-claims": "^6.0", "dacoto/laravel-domain-validation": "^4.0", "directorytree/ldaprecord": "^3.2", "guzzlehttp/guzzle": "^7.2", "inertiajs/inertia-laravel": "^1.0", "laravel/framework": "^11", "laravel/horizon": "^5.23", "laravel/nova": "^4.0", "laravel/octane": "^2.3", "laravel/passport": "^12.0", "laravel/sanctum": "^4.0", "laravel/tinker": "^2.8", "league/flysystem-aws-s3-v3": "^3.0", "litesaml/lightsaml": "^4.1", "livewire/livewire": "^3.4", "livewire/volt": "^1.0", "maatwebsite/excel": "^3.1", "owen-it/laravel-auditing": "^13.6", "propaganistas/laravel-phone": "^5.3", "prwnr/laravel-streamer": "^4.1", "sentry/sentry-laravel": "^4.4", "spatie/laravel-permission": "^6.0", "spatie/laravel-query-builder": "^6.1.0", "spatie/laravel-ray": "^1.35", "staudenmeir/laravel-adjacency-list": "^1.19", "stevebauman/location": "^7.1", "symfony/http-client": "^7.0", "symfony/postmark-mailer": "^7.0", "tightenco/ziggy": "^2.3", "knuckleswtf/scribe": "^4.38", "timokoerber/laravel-one-time-operations": "^1.4"}, "require-dev": {"fakerphp/faker": "^1.9.1", "jasonmccreary/laravel-test-assertions": "^2.3", "laravel/breeze": "^2.0", "mockery/mockery": "^1.4.4", "nunomaduro/collision": "^8.1", "pestphp/pest": "^3.0", "pestphp/pest-plugin-laravel": "^3.0", "spatie/laravel-ignition": "^2.0"}, "autoload": {"psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/"}, "files": ["app/helpers.php"]}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi"], "post-update-cmd": ["@php artisan vendor:publish --tag=laravel-assets --ansi --force"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi"], "format": "npx prettier . --write", "format:check": "npx prettier . --check", "docker:up": "docker build --tag 'start_backend' . && docker run -p 8000:8000 --rm 'start_backend'"}, "extra": {"laravel": {"dont-discover": []}}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true, "allow-plugins": {"pestphp/pest-plugin": true, "php-http/discovery": true}}, "minimum-stability": "stable", "prefer-stable": true, "repositories": [{"type": "composer", "url": "https://nova.laravel.com"}]}