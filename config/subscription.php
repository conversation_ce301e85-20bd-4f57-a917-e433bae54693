<?php

use App\Enums\SoftwareCode;
use App\Enums\SoftwarePackageCode;

return [
    /*
     *
     * Available software and packages
     *
     * */
    'software' => [
        /* Start software * */
        [
            'name' => SoftwareCode::Start->name(),
            'code' => SoftwareCode::Start->value,
            'is_available' => true,
            'packages' => [
                [
                    'name' => SoftwarePackageCode::Enterprise1->name(),
                    'code' => SoftwarePackageCode::Enterprise1->value,
                    'is_active' => true,
                    'features' => [],
                ],
                [
                    'name' => SoftwarePackageCode::Basic1->name(),
                    'code' => SoftwarePackageCode::Basic1->value,
                    'is_active' => true,
                    'features' => [],
                ],
            ],
        ],

        /* Attendance software * */
        [
            'name' => SoftwareCode::Attendance->name(),
            'code' => SoftwareCode::Attendance->value,
            'is_available' => true,
            'packages' => [
                [
                    'name' => SoftwarePackageCode::Enterprise1->name(),
                    'code' => SoftwarePackageCode::Enterprise1->value,
                    'is_active' => true,
                    'features' => [['code' => 'multi-shift'], ['code' => 'multi-location']],
                ],
                [
                    'name' => SoftwarePackageCode::Basic1->name(),
                    'code' => SoftwarePackageCode::Basic1->value,
                    'is_active' => true,
                    'features' => [],
                ],
            ],
        ],

        /* Visitors software * */
        [
            'name' => SoftwareCode::Visitors->name(),
            'code' => SoftwareCode::Visitors->value,
            'is_available' => true,
            'packages' => [
                [
                    'name' => SoftwarePackageCode::Enterprise1->name(),
                    'code' => SoftwarePackageCode::Enterprise1->value,
                    'is_active' => true,
                    'features' => [],
                ],
                [
                    'name' => SoftwarePackageCode::Basic1->name(),
                    'code' => SoftwarePackageCode::Basic1->value,
                    'is_active' => true,
                    'features' => [],
                ],
            ],
        ],

        /* Drive software * */
        [
            'name' => SoftwareCode::Drive->name(),
            'code' => SoftwareCode::Drive->value,
            'is_available' => true,
            'packages' => [
                [
                    'name' => SoftwarePackageCode::Enterprise1->name(),
                    'code' => SoftwarePackageCode::Enterprise1->value,
                    'is_active' => true,
                    'features' => [['code' => '10gp-per-employee']],
                ],
                [
                    'name' => SoftwarePackageCode::Basic1->name(),
                    'code' => SoftwarePackageCode::Basic1->value,
                    'is_active' => true,
                    'features' => [['code' => '5gp-per-employee']],
                ],
            ],
        ],
    ],
];
