<?php

namespace Database\Factories;

use App\Models\Plan;
use App\Models\Tenant;
use Illuminate\Database\Eloquent\Factories\Factory;

class PlanFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = Plan::class;

    /**
     * Define the model's default state.
     */
    public function definition(): array
    {
        return [
            'name' => $this->faker->name,
            'is_active' => $this->faker->boolean,
            'is_custom' => $this->faker->numberBetween(-100000, 100000),
            'customized_for_tenant_id' => Tenant::factory(),
            'price' => $this->faker->randomFloat(2, 100, 1000),
        ];
    }
}
