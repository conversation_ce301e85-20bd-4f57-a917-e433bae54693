<?php

namespace Database\Factories;

use App\Models\Plan;
use App\Models\PlanItem;
use App\Models\Software;
use App\Models\SoftwarePackage;
use Illuminate\Database\Eloquent\Factories\Factory;

class PlanItemFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = PlanItem::class;

    /**
     * Define the model's default state.
     */
    public function definition(): array
    {
        return [
            'name' => $this->faker->name,
            'software_id' => Software::factory(),
            'software_package_id' => SoftwarePackage::factory(),
            'plan_id' => Plan::factory(),
        ];
    }
}
