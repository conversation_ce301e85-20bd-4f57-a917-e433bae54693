<?php

namespace Database\Factories;

use App\Models\SoftwareFeature;
use App\Models\SoftwarePackage;
use Illuminate\Database\Eloquent\Factories\Factory;

class SoftwareFeatureFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = SoftwareFeature::class;

    /**
     * Define the model's default state.
     */
    public function definition(): array
    {
        return [
            'name' => $this->faker->name,
            'software_package_id' => SoftwarePackage::factory(),
            'is_active' => $this->faker->boolean,
        ];
    }
}
