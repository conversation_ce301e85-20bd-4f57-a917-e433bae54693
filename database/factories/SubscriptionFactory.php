<?php

namespace Database\Factories;

use App\Models\Plan;
use App\Models\Subscription;
use App\Models\Tenant;
use Illuminate\Database\Eloquent\Factories\Factory;

class SubscriptionFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = Subscription::class;

    /**
     * Define the model's default state.
     */
    public function definition(): array
    {
        return [
            'total_employees' => $this->faker->numberBetween(-100000, 100000),
            'start_at' => $this->faker->dateTime(),
            'end_at' => $this->faker->dateTime(),
            'tenant_id' => Tenant::factory(),
            'plan_id' => Plan::factory(),
            'price' => $this->faker->randomFloat(2, 100, 1000),
        ];
    }
}
