<?php

namespace Database\Factories;

use App\DTOs\Identifier;
use App\Interfaces\Authenticators\BaseAuthenticatorInterface;
use App\Interfaces\Authenticators\ExternalAuthenticatorInterface;
use App\Interfaces\Authenticators\RedirectAuthenticatorInterface;
use App\Models\Tenant;
use App\Models\TenantAuthenticatorConfig;
use Illuminate\Database\Eloquent\Factories\Factory;

class TenantAuthenticatorConfigFactory extends Factory
{
    protected $model = TenantAuthenticatorConfig::class;

    public function definition(): array
    {
        $baseAuthenticator = $this->fakeExternalAuthenticator();

        return [
            'tenant_id' => Tenant::factory(),
            'authenticator_class' => $baseAuthenticator::class,
            'config' => $this->faker->word(),
        ];
    }

    public function fakeExternalAuthenticator(): BaseAuthenticatorInterface|ExternalAuthenticatorInterface|RedirectAuthenticatorInterface
    {
        return new class implements
            BaseAuthenticatorInterface,
            ExternalAuthenticatorInterface,
            RedirectAuthenticatorInterface
        {
            public function configure(TenantAuthenticatorConfig $config): void
            {
            }

            public function authenticate(
                Identifier $identifier,
                Tenant $tenant,
                ?string $password = null
            ): bool {
                return true;
            }

            public function errors(): array
            {
                return [];
            }

            public function getRedirectTo()
            {
                return 'https://example.com';
            }
        };
    }
}
