<?php

namespace Database\Factories;

use App\DTOs\Identifier;
use App\Models\Tenant;
use App\Models\TenantAuthenticatorConfig;
use Illuminate\Database\Eloquent\Factories\Factory;

class TenantFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = Tenant::class;

    /**
     * Define the model's default state.
     */
    public function definition(): array
    {
        return [
            'name' => fake()->word(),
            'color' => fake()->hexColor(),
            'domain' => fake()->unique()->domainName,
        ];
    }

    public function allowSms(bool $allowSms): self
    {
        return $this->state(fn() => ['allow_sms' => $allowSms]);
    }

    public function withDomain(Identifier $identifier): self
    {
        return $this->state(
            fn() => [
                'domain' => Identifier::extractDomain($identifier->value()),
                'allow_sms' => $identifier->isPhone(),
            ]
        );
    }

    public function externalAuthenticator(): static
    {
        return $this->has(TenantAuthenticatorConfig::factory(), 'authenticatorConfig');
    }
}
