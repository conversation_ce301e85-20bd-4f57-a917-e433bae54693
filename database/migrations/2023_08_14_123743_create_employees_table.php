<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::disableForeignKeyConstraints();

        Schema::create('employees', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->string('first_name');
            $table->string('last_name')->nullable();
            $table->string('email')->index();
            $table->string('password');
            $table->boolean('is_active')->default(true);
            $table->string('phone');
            $table->string('position')->nullable();
            $table->string('number')->nullable(); // employee number (Represents what's in the ERP)
            $table->foreignUuid('department_id')->nullable()->constrained();
            $table->foreignUuid('tenant_id')->constrained();
            $table->unique(['email', 'tenant_id']);
            $table->unique(['number', 'tenant_id']);
            $table->timestamps();
        });

        Schema::enableForeignKeyConstraints();
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('employees');
    }
};
