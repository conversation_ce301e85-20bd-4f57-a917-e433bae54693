<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\DB;

return new class extends Migration {
    public function up(): void
    {
        DB::statement('
            CREATE OR REPLACE VIEW employee_managers_view AS
            SELECT
                e.id AS employee_id,
                e.tenant_id AS employee_tenant_id,
                COALESCE(e.manager_id, d.manager_id) AS current_manager_id
            FROM
                employees e
            LEFT JOIN
                departments d ON e.department_id = d.id AND e.tenant_id = d.tenant_id;
        ');
    }

    public function down(): void
    {
        DB::statement('DROP VIEW IF EXISTS employee_managers_view');
    }
};
