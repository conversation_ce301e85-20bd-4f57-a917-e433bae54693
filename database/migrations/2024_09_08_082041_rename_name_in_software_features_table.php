<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up(): void
    {
        Schema::table('software_features', function (Blueprint $table) {
            $table->renameColumn('name', 'code');
        });
    }

    public function down(): void
    {
        Schema::table('software_features', function (Blueprint $table) {
            $table->renameColumn('code', 'name');
        });
    }
};
