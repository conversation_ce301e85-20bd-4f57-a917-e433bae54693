<?php

use App\Models\SubscriptionItem;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up(): void
    {
        Schema::table('subscription_items', function (Blueprint $table) {
            $table
                ->foreignUuid('tenant_id')
                ->after('software_package_id')
                ->nullable()
                ->constrained();
        });

        SubscriptionItem::with('subscription')->each(
            fn(SubscriptionItem $subscriptionItem) => $subscriptionItem->update([
                'tenant_id' => $subscriptionItem->subscription->tenant_id,
            ])
        );
    }
};
