<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up(): void
    {
        Schema::create('otps', function (Blueprint $table) {
            $table->id();
            $table->string('value')->index();
            $table->string('identifier')->index();
            $table->enum('identifier_type', ['email', 'phone'])->index();
            $table->foreignUuid('tenant_id')->constrained();
            $table->foreignUuid('employee_id')->constrained();
            $table->dateTime('expires_at')->index();
            $table->dateTime('used_at')->nullable()->index();
            $table->integer('tries')->default(0);
            $table->timestamps();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('otps');
    }
};
