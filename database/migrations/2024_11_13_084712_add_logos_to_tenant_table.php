<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up(): void
    {
        if (Schema::hasColumn('tenants', 'logo')) {
            Schema::table('tenants', function (Blueprint $table) {
                $table->dropColumn('logo');
            });
        }

        Schema::table('tenants', function (Blueprint $table) {
            $table->string('colored_logo')->nullable();
            $table->string('white_logo')->nullable();
        });
    }

    public function down(): void
    {
        Schema::table('tenants', function (Blueprint $table) {
            $table->dropColumn('colored_logo');
            $table->dropColumn('white_logo');
        });
    }
};
