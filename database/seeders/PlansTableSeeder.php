<?php

namespace Database\Seeders;

use App\Models\Plan;
use App\Models\PlanItem;
use App\Models\Software;
use Illuminate\Database\Seeder;

class PlansTableSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $plans = [
            [
                'name' => 'Enterprise',
                'price' => 50030,
                'plan_items' => [
                    [
                        'name' => 'Attendance Enterprise',
                        'software_code' => 'att',
                        'software_package_code' => 'enterprise-1',
                    ],
                    [
                        'name' => 'Drive Enterprise',
                        'software_code' => 'drive',
                        'software_package_code' => 'enterprise-1',
                    ],
                ],
            ],
            [
                'name' => 'Basic',
                'price' => 25000,
                'plan_items' => [
                    [
                        'name' => 'Attendance basic',
                        'software_code' => 'att',
                        'software_package_code' => 'basic-1',
                    ],
                    [
                        'name' => 'Drive basic',
                        'software_code' => 'drive',
                        'software_package_code' => 'basic-1',
                    ],
                ],
            ],
        ];

        foreach ($plans as $plan) {
            $planModel = Plan::firstOrCreate(
                ['name' => $plan['name']],
                ['price' => $plan['price'], 'is_active' => true]
            );
            foreach ($plan['plan_items'] as $item) {
                $software = Software::query()
                    ->with('softwarePackages')
                    ->firstWhere('code', $item['software_code']);
                $softwarePackage = $software->softwarePackages
                    ->where('code', $item['software_package_code'])
                    ->first();
                PlanItem::firstOrCreate(
                    [
                        'plan_id' => $planModel->id,
                        'name' => $item['name'],
                    ],
                    [
                        'software_id' => $software->id,
                        'software_package_id' => $softwarePackage->id,
                    ]
                );
            }
        }
    }
}
