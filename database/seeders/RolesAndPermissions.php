<?php

namespace Database\Seeders;

use App\Models\Role;
use App\Models\Software;
use Illuminate\Database\Seeder;
use Illuminate\Support\Collection;
use function collect;

class RolesAndPermissions extends Seeder
{
    public function run(): void
    {
        $this->roles()->each(
            fn($role) => Role::updateOrCreate(
                ['name' => $role['name']],
                ['software_id' => $role['software_id']]
            )
        );
    }

    public function roles(): Collection
    {
        if (!Software::exists()) {
            throw new \Exception('Software table is empty, run SetupSoftwareTablesSeeder first');
        }

        [
            'start' => $start,
            'attendance' => $attendance,
            'visitors' => $visitors,
        ] = Software::list();

        return collect([
            // Start
            ['name' => 'start-admin', 'software_id' => $start->id],
            ['name' => 'start-developer', 'software_id' => $start->id],

            // Attendance
            ['name' => 'attendance-hr', 'software_id' => $attendance->id],
            ['name' => 'attendance-developer', 'software_id' => $attendance->id],
            ['name' => 'attendance-dashboard-viewer', 'software_id' => $attendance->id],

            // Visitors
            ['name' => 'visitors-owner', 'software_id' => $visitors->id],
            ['name' => 'visitors-manager', 'software_id' => $visitors->id],
            ['name' => 'visitors-staff', 'software_id' => $visitors->id],
            ['name' => 'visitors-guard', 'software_id' => $visitors->id],
        ]);
    }
}
