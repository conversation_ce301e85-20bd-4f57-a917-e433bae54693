<?php

namespace Database\Seeders;

use App\Models\Tenant;
use App\Models\TenantAuthenticatorConfig;
use Illuminate\Database\Seeder;

class SampleLdapAuthenticatorConfig extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $tenant = Tenant::find('9a8a1c7b-1d5c-4741-8ed0-2577cd2b65bb');

        TenantAuthenticatorConfig::updateOrCreate([
            'tenant_id' => $tenant->id,
            'config' => [
                'ldap_host' => 'docker.for.mac.host.internal',
                'ldap_port' => 1389,
                'ldap_base_dn' => 'dc=example,dc=org',
                'ldap_username' => 'cn=admin,dc=example,dc=org',
                'ldap_password' => 'adminpassword',
                'ldap_username_field' => 'cn',
                'ldap_distinguishedname_field' => 'dn',
            ],
            'authenticator_class' => \App\Authenticators\LdapAuthenticator::class,
        ]);
    }
}
