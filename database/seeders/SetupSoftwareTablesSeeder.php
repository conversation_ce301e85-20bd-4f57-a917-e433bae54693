<?php

namespace Database\Seeders;

use App\Models\Software;
use App\Models\SoftwareFeature;
use App\Models\SoftwarePackage;
use Illuminate\Database\Seeder;
use function config;

class SetupSoftwareTablesSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        foreach (config('subscription.software') as $software) {
            $softwareModel = Software::updateOrCreate(
                [
                    'name' => $software['name'],
                    'code' => $software['code'],
                ],
                ['is_available' => $software['is_available']]
            );

            foreach ($software['packages'] as $package) {
                $packageModel = SoftwarePackage::updateOrCreate(
                    [
                        'software_id' => $softwareModel->id,
                        'code' => $package['code'],
                    ],
                    [
                        'name' => $package['name'],
                        'is_active' => $package['is_active'],
                    ]
                );

                foreach ($package['features'] as $feature) {
                    SoftwareFeature::updateOrCreate(
                        [
                            'code' => $feature['code'],
                            'software_package_id' => $packageModel->id,
                        ],
                        ['is_active' => true]
                    );
                }
            }
        }
    }
}
