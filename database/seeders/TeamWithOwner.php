<?php

namespace Database\Seeders;

use App\Models\Employee;
use App\Models\Plan;
use App\Models\Subscription;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class TeamWithOwner extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $this->call([
            SetupSoftwareTablesSeeder::class,
            PlansTableSeeder::class,
            RolesAndPermissions::class,
        ]);

        // Employee created with a team
        $employee = Employee::factory()->create();
        // get the team
        $tenant = $employee->tenant;

        // set the employee as owner
        $tenant->owner_id = $employee->id;
        $tenant->save();

        // get enterprise plan
        $plan = Plan::firstWhere('name', 'Enterprise');
        // subscribe to a plan
        DB::transaction(function () use ($tenant, $plan) {
            $subscription = Subscription::create([
                'total_employees' => 50,
                'start_at' => now(),
                'end_at' => now()->addMonths(4),
                'tenant_id' => $tenant->id,
                'plan_id' => $plan->id,
                'price' => $plan->price, // not correct, but for testing
            ]);

            $subscriptionItems = [];
            foreach ($plan->planItems as $item) {
                $subscriptionItems[] = [
                    'software_id' => $item->software_id,
                    'software_package_id' => $item->software_package_id,
                ];
            }

            $subscription->subscriptionItems()->createMany($subscriptionItems);
        });

        echo 'employee id: ' . $employee->id . PHP_EOL;
        echo 'employee email: ' . $employee->email . PHP_EOL;
        echo 'tenant id: ' . $tenant->id . PHP_EOL;
    }
}
