<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use LdapRecord\Connection;

class TestLdapUsersTableSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create a new connection:
        $connection = new Connection([
            'hosts' => ['docker.for.mac.host.internal'],
            'port' => 1389,
            'base_dn' => 'dc=example,dc=org',
            'username' => 'cn=admin,dc=example,dc=org',
            'password' => 'adminpassword',
        ]);

        try {
            $connection->connect();

            echo 'Successfully connected!' . PHP_EOL;
        } catch (\LdapRecord\Auth\BindException $e) {
            $error = $e->getDetailedError();

            echo $error->getErrorCode();
            echo $error->getErrorMessage();
            echo $error->getDiagnosticMessage();
        }

        $user = $connection->query()->where('cn', '=', 'user01')->firstOrFail();

        $dn = @$user['distinguishedname'] ?: $user['dn'];
        if ($connection->auth()->attempt($dn, 'password')) {
            info('authenticated');
        } else {
            info('not authenticated');
        }

        //        dump($user);
        //        dump($connection->auth()->attempt('user01', 'bitnami1'));
    }
}
