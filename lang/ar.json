{"start": "ستارت", "attendance": "الحضور", "visitors": "الزوار", "start-admin": "مدير ستارت", "start-admin-description": "أعلى مستوى وصول في النظام يمكنه الوصول إلى لوحة التحكم ومراجعة وتعديل جميع البيانات المتاحة", "start-developer": "مطور ستارت", "start-developer-description": "المطور يمكنه الوصول إلى صفحة التكامل دون الوصول إلى أجزاء أخرى من النظام", "attendance-developer": "مطور الحضور", "attendance-developer-description": "المطور يمكنه الوصول إلى صفحة التكامل دون الوصول إلى أجزاء أخرى من النظام", "attendance-dashboard-viewer": "لوحة التحكم في الحضور", "attendance-dashboard-viewer-description": "يمكن للمستخدم الوصول إلى لوحة التحكم ومراجعة البيانات المتاحة دون لبقية النظام", "attendance-admin": "مدير الحضور", "attendance-admin-description": "أعلى مستوى وصول في النظام يمكنه الوصول إلى لوحة التحكم ومراجعة وتعديل جميع البيانات المتاحة", "attendance-hr": "موظف الموارد البشرية", "attendance-hr-description": "يمكن للمستخدم HR التحكم في بيانات الموظفين والإدارات والتحقق من التقارير دون الوصول إلى الإعدادات العامة للنظام", "visitors-owner": "مدير نظام الزوار", "visitors-owner-description": "أعلى مستوى وصول في النظام يمكنه الوصول إلى لوحة التحكم ومراجعة وتعديل جميع البيانات المتاحة", "visitors-manager": "مدير فرع في الزوار", "visitors-manager-description": "يمكن ان يدير فرع او اكثر", "visitors-staff": "موظف في الزوار", "visitors-staff-description": "يمكن ان يرسل دعوات ", "visitors-guard": "حارس الزوار", "visitors-guard-description": "مسؤول عن إدارة الزوار في الموقع، وتسجيل الدخول والخروج للزوار، ولديه وصول محدود إلى الإعدادات الإدارية.", "The Employee has been created successfully.": "تم إنشاء الموظف بنجاح.", "The Department has been created successfully.": "تم إنشاء القسم بنجاح.", "The department has been updated successfully.": "تم تحديث القسم بنجاح.", "The department has been deleted successfully.": "تم حذف القسم بنجاح.", "The Employee has been updated successfully.": "تم تحديث الموظف بنجاح.", "settings updated successfully": "تم تحديث الإعدادات بنجاح", "CSV file imported successfully": "تم استيراد ملف CSV بنجاح", "employee deactivated successfully": "تم تعطيل الموظف بنجاح", "employee activated successfully": "تم تفعيل الموظف بنجاح", "Time remaining": "الوقت الباقي", "Resend Code": "اعادة ارسال الكود", "Verify your email": "التحقق من البريد الالكتروني", "Enter the verification code that was sent via e-mail to": "ادخل الرمز المرسل الى بريدك الالكتروني", "Log in": "تسجيل دخول", "Next": "التالي", "Back": "رجوع", "Email": "البريد الالكتروني", "Work Email": "البريد الالكتروني", "Login with your company credentials": "قم بتسجيل الدخول ببيانات اعتماد شركتك ", "Welcome!": "مرحبا!", "Hi": "مرحبا", "We received a request to access your account": "لقد تلقينا طلبًا للوصول إلى حسابك", "Your verification code is": "رمز التحقق الخاص بك هو", "If you did not request this code, it is possible that someone else is trying to access the account": "إذا لم تطلب هذا الرمز، فمن الممكن أن يكون شخص آخر يحاول الوصول إلى الحساب", "Do not forward or give this code to anyone": "لا تقم بإعادة توجيه أو إعطاء هذا الرمز لأي شخص", "The code is valid for 2 minutes and usable only once": "الرمز صالح لمدة دقيقتين ويمكن استخدامه مرة واحدة فقط", "Thank you": "شكرا لك", "Nawart Verification Code": "<PERSON><PERSON><PERSON> التحقق من <PERSON>", "Department not found": "القسم غير موجود", "employee already activated": "الموظف مفعّل بالفعل", "employee already deactivated": "الموظف معطّل بالفعل", "Employee deactivated successfully": "تم تعطيل الموظف بنجاح", "Employee activate successfully": "تم تنشيط الموظف بنجاح", "Employee not found": "الموظف غير موجود", "New employee  has been created successfully": "تم إنشاء الموظف الجديد بنجاح", "employee updated successfully": "تم تحديث بيانات الموظف بنجاح", "department updated successfully": "تم تحديث القسم بنجاح", "token has been deleted successfully": "تم حذف الر<PERSON>ز بنجاح", "Your Access Token is: :token": " رمز الوصول الخاص بك هو: :token", "Department that has employees cannot be deleted": "لا يمكن حذف القسم الذي يحتوي على موظفين", "Department that has sub-departments cannot be deleted": "لا يمكن حذف القسم الذي يحتوي على أقسام فرعية", "Select Company": "اختر الشركة", "Cookies are disabled in your browser. Please enable cookies to continue.": "ملفات تعريف الارتباط (الكوكيز Cookies) معطلة في متصفحك. يرجى تفعيلها للمتابعة.", "Chrome": "متصفح Chrome", "Safari": "متص<PERSON><PERSON>", "How to enable cookies": "كيفية تفعيل ملفات تعريف الارتباط (الكوكيز Cookies)", "One time password resent successfully": "تم ارسال رمز التحقق لمرة واحدة بنجاح", "Your Account is not Active. Please Contact Your Administrator": "حسابك غير نشط. يرجى الاتصال بالمسؤول الخاص بك", "Your Tenant Account is not Active ,Please Contact Your Administrator": "حساب المستأجر الخاص بك غير نشط. يرجى التواصل مع المسؤولين ", "The Email does not exists on our systems": "البريد الإلكتروني غير موجود في نظامنا", "Welcome": "نورتنا", "Hi :name You have been added to the Nawart app, and we are delighted to have you on board with us.": "اهلا بك :name، تمت دعوتك لتطبيق نورت ونتطلع لوجودك معنا.", "To start your journey, download the app": "قم بتحميل التطبيق لبدء رحلتك", "This Email does not exist in our systems": "هذا الحساب غير موجود في نظامنا", "Your session is expired, we sent a new code, please try again.": "انتهت جلستك، لقد أرسلنا رمزًا جديدًا، يرجى المحاولة مرة أخرى.", "One time password sent successfully": "تم إرسال كلمة المرور لمرة واحدة بنجاح", "Did not receive code": "لم تقم بالحصول علي رمز التحقق؟", "department does not exist": "القسم غير موجود", "manager does not exist": "المدير غير موجود", "tags was added to employees successfully": "تمت إضافة تصنيفات إلى الموظفين بنجاح", "There was an error on row :row. :message": "حدث خطأ في الصف :row. :message", "and :count more error": "و :count خ<PERSON><PERSON> آخر", "and :count more errors": "و :count أخطاء أخرى", "One time password is wrong, please try again.": "رمز التحقق خاطى، يرجى المحاولة مرة أخرى.", "Tag created successfully": "تم إنشاء التصنيف بنجاح", "Tag updated successfully": "تم تحديث التصنيف بنجاح", "Tag deleted successfully": "تم حذف التصنيف بنجاح", "Your message has been sent successfully. Track its progress via your email": "تم إرسال رسالتك بنجاح، تابع تقدمها عبر بريدك الإلكتروني", "Employees department has been updated successfully": "تم تحديث إدارة الموظفين بنجاح", "Employees direct manager has been updated successfully": "تم تحديث مدير الموظفين بنجاح"}