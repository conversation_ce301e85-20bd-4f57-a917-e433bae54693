{"start": "Start", "attendance": "Attendance", "visitors": "Visitors", "start-admin": "Start Admin", "start-admin-description": "The highest level of access in the system can access the dashboard and review and modify all available data", "start-developer": "Start Developer", "start-developer-description": "The developer user can access integration page without having access to the other parts of the system", "attendance-developer": "Attendance Developer", "attendance-developer-description": "The developer user can access integration page without having access to the other parts of the system", "attendance-dashboard-viewer": "Attendance Dashboard Viewer", "attendance-dashboard-viewer-description": "The user can access the dashboard and review available data without access to the rest of the system", "attendance-admin": "Attendance Admin", "attendance-admin-description": "The highest level of access in the system can access the dashboard and review and modify all available data", "attendance-hr": "HR Employee", "visitors-owner": "Visitors Owner", "visitors-owner-description": "The highest level of access in the system can access the dashboard and review and modify all available data.", "visitors-manager": "Visitors Manager", "visitors-manager-description": "Can manage one or more branches.", "visitors-staff": "Visitors Staff", "visitors-staff-description": "Can send invitations.", "attendance-hr-description": "The HR user can control employee and department data and verify reports without access to the general system settings", "visitors-admin": "Visitors Admin", "visitors-admin-description": "The highest level of access in the system can access the dashboard and review and modify all available data", "visitors-guard": "Visitors Guard", "visitors-guard-description": "Responsible for managing visitors on site, registering visitor check-ins and check-outs, and has limited access to administrative settings.", "The Employee has been created successfully.": "The Employee has been created successfully.", "The Department has been created successfully.": "The Department has been created successfully.", "The department has been updated successfully.": "The department has been updated successfully.", "The department has been deleted successfully.": "The department has been deleted successfully.", "The Employee has been updated successfully.": "The Employee has been updated successfully.", "settings updated successfully": "Settings updated successfully", "CSV file imported successfully": "CSV file imported successfully", "employee deactivated successfully": "Employee deactivated successfully", "employee activated successfully": "Employee activated successfully", "Time remaining": "Time remaining", "Resend Code": "Resend Code", "Verify your email": "Verify your email", "Enter the verification code that was sent via e-mail to": "Enter the verification code that was sent via e-mail to", "Log in": "Log in", "Next": "Next", "Back": "Back", "Email": "Email", "Work Email": "Work Email", "Login with your company credentials": "Login with your company credentials", "Welcome!": "Welcome!", "Hi": "Hi", "We received a request to access your account": "We received a request to access your account", "Your verification code is": "Your verification code is", "If you did not request this code, it is possible that someone else is trying to access the account": "If you did not request this code, it is possible that someone else is trying to access the account", "Do not forward or give this code to anyone": "Do not forward or give this code to anyone", "The code is valid for 2 minutes and usable only once": "The code is valid for 2 minutes and usable only once", "Thank you": "Thank you", "Nawart Verification Code": "Nawart Verification Code", "Department not found": "Department not found", "employee already activated": "Employee already activated", "employee already deactivated": "Employee already deactivated", "Employee deactivated successfully": "Employee deactivated successfully", "Employee activated successfully": "Employee activated successfully", "Employee not found": "Employee not found", "New employee has been created successfully": "New employee has been created successfully", "employee updated successfully": "Employee updated successfully", "department updated successfully": "Department updated successfully", "token has been deleted successfully": "Token has been deleted successfully", "Your Access Token is: :token": "Your Access Token is: :token", "Department that has employees cannot be deleted": "Department that has employees cannot be deleted", "Department that has sub-departments cannot be deleted": "Department that has sub-departments cannot be deleted", "Select Company": "Select Company", "Cookies are disabled in your browser. Please enable cookies to continue.": "Cookies are disabled in your browser. Please enable cookies to continue.", "Chrome": "Chrome", "Safari": "Safari", "How to enable cookies": "How to enable cookies", "One time password resent successfully": "One time password resent successfully.", "Your Account is not Active. Please Contact Your Administrator": "Your Account is not Active. Please Contact Your Administrator", "Your Tenant Account is not Active, Please Contact Your Administrator": "Your Tenant Account is not Active. Please Contact Your Administrator", "The Email does not exist on our systems": "The Email does not exist on our systems", "Welcome": "Welcome", "Hi :name, You have been added to the Nawart app, and we are delighted to have you on board with us.": "Hi :name, You have been added to the Nawart app, and we are delighted to have you on board with us.", "To start your journey, download the app": "To start your journey, download the app", "This Email does not exist in our systems": "This Email does not exist in our systems", "Select Team": "Select Team", "Your session is expired, we sent a new code, please try again.": "Your session is expired, we sent a new code, please try again.", "Did not receive code": "Did not receive code", "One time password sent successfully": "One time password sent successfully"}