import { Link, router, usePage } from "@inertiajs/react";
import React, { useState } from "react";
import LanguageIcon from "./LanguageIcon";
export default function LanguageSwitcher() {
    const { locale } = usePage().props;

    const [currentLocale, setCurrentLocale] = useState(locale);

    const toggleLocale = () => {
        const newLocale = currentLocale === "ar" ? "en" : "ar";
        e.preventDefault();
        router.get(`language/${newLocale}`);
        setCurrentLocale(newLocale);
    };

    return (
        <Link
            dir={currentLocale === "ar" ? "rtl" : "ltr"}
            href={currentLocale === "ar" ? "/language/en" : "/language/ar"}
            className="flex items-center gap-x-2 text-sm absolute left-8 top-8"
            onClick={() => toggleLocale()}
        >
            <LanguageIcon />
            {currentLocale === "ar" ? "AR" : "EN"}
        </Link>
    );
}
