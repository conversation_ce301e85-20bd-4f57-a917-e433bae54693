import React, { forwardRef, useEffect, useRef } from "react";

export default forwardRef(function TextInput(
    { type = "text", className = "", isFocused = false, ...props },
    ref,
) {
    const input = ref ? ref : useRef();

    useEffect(() => {
        if (isFocused) {
            input.current.focus();
        }
    }, []);

    return (
        <input
            {...props}
            type={type}
            className={`border text-gray-900 text-sm rounded-lg block w-full p-2.5 border-gray-300 outline-none focus:ring-1 focus:ring-emerald-500 focus:border-emerald-500 ${className}`}
            ref={input}
        />
    );
});
