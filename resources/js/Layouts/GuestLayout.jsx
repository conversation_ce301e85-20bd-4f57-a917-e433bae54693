import React from "react";
import ApplicationLogo from "@/Components/ApplicationLogo.jsx";
import FlashMessage from "@/Components/FlashMessage";
import LanguageSwitcher from "@/Components/LanguageSwitcher";
export default function Guest({ children }) {
    return (
        <>
            <LanguageSwitcher />
            <div className="flex flex-col items-center justify-center px-2 md:px-6 py-8 mx-auto md:h-screen lg:py-0">
                <a className="flex items-center mb-6 text-2xl font-semibold text-gray-900 ">
                    <ApplicationLogo className="w-20 h-20 fill-current text-gray-500" />
                </a>
                <div className="w-full bg-white rounded-lg shadow md:mt-0 sm:max-w-md xl:p-0 py-4">
                    <div className="p-6 space-y-4 md:space-y-6 sm:p-8">
                        <FlashMessage />
                        {children}
                    </div>
                </div>
            </div>
        </>
    );
}
