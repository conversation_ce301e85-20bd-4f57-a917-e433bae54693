import GuestLayout from "@/Layouts/GuestLayout";
import { Head, router, useForm, usePage } from "@inertiajs/react";
import React, { useState } from "react";
import OTPInput from "react-otp-input";
import PrimaryButton from "@/Components/PrimaryButton.jsx";
import InputError from "@/Components/InputError.jsx";
import { useTimer } from "react-timer-hook";
import { useTranslation } from "@/translations";
import { ArrowLeft02Icon, ArrowRight02Icon } from "hugeicons-react";
import TextInput from "@/Components/TextInput";

type Props = {
    identifier: {
        value: string;
        type: "email" | "phone";
    };
    secondsToExpire: number;
    allowSms: boolean;
};

export default function OtpForm({ identifier, secondsToExpire, allowSms }: Props) {
    const [submitDisabled, setSubmitDisabled] = useState(false);

    const { t } = useTranslation();
    const { errors, locale } = usePage().props;

    const { data, setData, post } = useForm({ otp: "" });

    const getExpiryTimestamp = () => new Date(new Date().getTime() + secondsToExpire * 1000);

    const { seconds, minutes, isRunning, restart } = useTimer({
        expiryTimestamp: getExpiryTimestamp(),
    });

    const formattedRemainingTime = `${minutes}:${`${seconds % 60}`.padStart(2, "0")}`;

    const submit = () => {
        setSubmitDisabled(true);

        post(route("otp.store"), {
            onError: () => setSubmitDisabled(false),
            onCancel: () => setSubmitDisabled(false),
        });
    };

    const resendOtp = (identifierType: "email" | "phone" | null = null) => {
        post(route("resend-otp", { identifier_type: identifierType }), {
            // for some reason, we need to delay the restart function
            onSuccess: () => setTimeout(() => restart(getExpiryTimestamp()), 1000),
        });
    };

    return (
        <GuestLayout>
            <Head title={t("Login")} />
            <div>
                <div className="mb-6">
                    <div className={"flex gap-x-2 items-center"}>
                        {locale === "ar" ? (
                            <ArrowRight02Icon
                                size={40}
                                className={"cursor-pointer hover:bg-gray-100 rounded transition"}
                                onClick={() => router.get(route("identifier-form.create"))}
                            />
                        ) : (
                            <ArrowLeft02Icon
                                size={40}
                                className={"cursor-pointer hover:bg-gray-100 rounded transition"}
                                onClick={() => router.get(route("identifier-form.create"))}
                            />
                        )}
                        <p className="text-xl md:text-2xl font-bold">
                            {identifier.type === "email"
                                ? t("Verify your email")
                                : t("Verify your number")}
                        </p>
                    </div>
                    <p className="text-sm leading-tight font-normal mt-2 text-gray-500">
                        {t("Enter the verification code that was sent to")}
                        <br />
                        <span dir={"ltr"} className="text-emerald-700 mt-2">
                            {identifier.value}
                        </span>
                    </p>
                </div>

                <form onSubmit={submit}>
                    <div className="mt-4">
                        <OTPInput
                            shouldAutoFocus={true}
                            containerStyle={"flex mb-2 gap-x-4 justify-center [direction:ltr]"}
                            value={data.otp}
                            onChange={(otp) => setData("otp", otp)}
                            inputType={"tel"}
                            inputStyle={"block flex-1 h-[78px] max-w-20 font-extrabold !text-4xl"}
                            numInputs={4}
                            renderSeparator={""}
                            renderInput={(props) => <TextInput id={"otp-input"} {...props} />}
                        />
                        <InputError message={errors.otp} className="mt-2 mb-4" />
                    </div>
                    <div className="mt-4 flex flex-col gap-y-4">
                        <div className={"flex justify-center"}>
                            {isRunning ? (
                                <p className="py-1 text-gray-500">
                                    {`${t("Resend Code in")} ${formattedRemainingTime}`}
                                </p>
                            ) : (
                                <a
                                    onClick={() => resendOtp()}
                                    className="text-center text-emerald-700 text-sm font-bold cursor-pointer"
                                >
                                    {t("Resend Code")}
                                </a>
                            )}
                        </div>
                        {allowSms && identifier.type === "phone" ? (
                            <a
                                onClick={() => resendOtp("email")}
                                className="text-center text-emerald-700 text-sm font-bold cursor-pointer"
                            >
                                {t("Resend Code via Email")}
                            </a>
                        ) : null}
                    </div>

                    <div className="mt-9">
                        <PrimaryButton
                            onClick={submit}
                            type={"button"}
                            className={`w-full ${submitDisabled ? "opacity-50 cursor-not-allowed" : ""}`}
                        >
                            {t("Login")}
                        </PrimaryButton>
                    </div>
                </form>
            </div>
        </GuestLayout>
    );
}
