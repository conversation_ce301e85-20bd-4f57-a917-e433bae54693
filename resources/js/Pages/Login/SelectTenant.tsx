import PrimaryButton from "@/Components/PrimaryButton";
import GuestLayout from "@/Layouts/GuestLayout";
import { Head, useForm } from "@inertiajs/react";
import React from "react";
import { useTranslation } from "@/translations";
import InputError from "@/Components/InputError";

type Props = {
    employees: {
        tenant: {
            id: number;
            name: string;
        };
        email: string;
    }[];
};

export default function SelectTenant({ employees }: Props) {
    const { t } = useTranslation();

    const { post, data, setData, processing, errors } = useForm<{ tenant: number | null }>({
        tenant: null,
    });

    const submit = () => post(route("select-tenants.select"));

    return (
        <GuestLayout>
            <Head title={t("Select Company")} />
            <div>
                <div className={"mb-8"}>
                    <p className="font-bold mb-2 text-lg">{t("Select Company")}</p>
                    <div className={"flex flex-col gap-y-2"}>
                        {employees.map((employee) => (
                            <div className={"flex gap-x-2 items-center"} key={employee.tenant.id}>
                                <input
                                    type="radio"
                                    id={`tenant${employee.tenant.id}`}
                                    value={employee.tenant.id}
                                    required
                                    className="w-4 h-4 text-emerald-600 bg-gray-100 border-gray-300
                                    rounded focus:ring-emerald-500 dark:focus:ring-emerald-600
                                    dark:ring-offset-gray-800 focus:ring-2
                                    dark:bg-gray-700 dark:border-gray-600"
                                    name="tenant"
                                    checked={data.tenant === employee.tenant.id}
                                    onChange={() => setData({ tenant: employee.tenant.id })}
                                />
                                <label
                                    htmlFor={`tenant${employee.tenant.id}`}
                                    className={`block font-medium text-sm text-gray-700`}
                                >
                                    {employee.tenant.name}
                                </label>
                            </div>
                        ))}
                        <InputError message={errors.tenant} className="mt-2 mb-4" />
                    </div>
                </div>
                <PrimaryButton
                    onClick={submit}
                    className={`w-full ${processing || !data.tenant ? "opacity-50 cursor-not-allowed" : ""}`}
                >
                    {t("Login")}
                </PrimaryButton>
            </div>
        </GuestLayout>
    );
}
