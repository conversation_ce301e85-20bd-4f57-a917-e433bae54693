<?php

use App\Providers\RouteServiceProvider;
use Livewire\Volt\Component;
use Illuminate\Validation\ValidationException;
use Livewire\Attributes\Layout;
use Illuminate\Auth\Events\Lockout;
use Illuminate\Support\Facades\RateLimiter;
use Illuminate\Support\Str;
use App\Models\Tenant;

new #[Layout('layouts.guest')] class extends Component {
    #[\Livewire\Attributes\Url]
    public string $email;

    #[\Livewire\Attributes\Url]
    public string $tenantId;

    #[\Livewire\Attributes\Url]
    public string|null $tenantsCount;

    public string $password;

    public function login(): void
    {
        $this->validate([
            'password' => 'required',
        ]);

        // get team authenticator
        $tenant = Tenant::with('authenticatorConfig')->findOrFail($this->tenantId);
        $authenticatorConfig = $tenant->authenticatorConfig;

        /**
         * @var $authenticator \App\Interfaces\Authenticators\BaseAuthenticatorInterface
         */
        $authenticator = new $authenticatorConfig->authenticator_class();

        $authenticator->configure($authenticatorConfig);
        $this->ensureIsNotRateLimited();

        if (!$authenticator->authenticate($this->email, $tenant, $this->password)) {
            RateLimiter::hit($this->throttleKey());

            throw ValidationException::withMessages([
                'password' => $authenticator->errors(),
            ]);
        }

        RateLimiter::clear($this->throttleKey());

        session()->regenerate();

        $this->redirect(session('url.intended', config('app.frontend_url')), navigate: true);
    }

    protected function ensureIsNotRateLimited(): void
    {
        if (!RateLimiter::tooManyAttempts($this->throttleKey(), 5)) {
            return;
        }

        event(new Lockout(request()));

        $seconds = RateLimiter::availableIn($this->throttleKey());

        throw ValidationException::withMessages([
            'password' => trans('auth.throttle', [
                'seconds' => $seconds,
                'minutes' => ceil($seconds / 60),
            ]),
        ]);
    }

    protected function throttleKey(): string
    {
        return Str::transliterate(Str::lower($this->email) . '|' . request()->ip());
    }
};
?>

<div>
    <div class="mb-6">
        <p class="text-2xl font-bold ">{{ __('Welcome!') }}</p>
        <p class="text-sm leading-tight font-normal text-gray-500 mt-2">{{ __('Login with your company credentials') }}
        </p>
    </div>
    <x-auth-session-status class="mb-4" :status="session('status')" />
    <form wire:submit="login">
        <div id="email-field">
            <!-- Email Address -->
            <div>
                <x-input-label for="email" :value="__('Work Email')" />
                <x-text-input wire:model="email" id="email" class="block mt-1 w-full cu" type="email" :disabled=true
                              autofocus autocomplete="username" />
                <x-input-error :messages="$errors->get('email')" class="mt-2" />
            </div>
        </div>
        <form wire:submit="login">
            <div class="mt-4 transition-all" id="password-field">
                <x-input-label for="password" :value="__('Password')" />

                <x-text-input wire:model="password" id="password" class="block mt-1 w-full" type="password"
                              name="password" required autocomplete="current-password" />

                <x-input-error :messages="$errors->get('password')" class="mt-2" />
            </div>
            <div class="flex items-center justify-between mt-4" id="bottom">
                <div>
                    @if ($tenantsCount > 1)
                        <a href="{{ route('login.select-tenant') }}?email={{ $email }}" wire:navigate>
                            <x-secondary-button id="back-btn">
                                {{ __('Back') }}
                            </x-secondary-button>
                        </a>
                    @else
                        <a href="{{ route('identifier-form.create') }}?email={{ $email }}" wire:navigate>
                            <x-secondary-button id="back-btn">
                                {{ __('Back') }}
                            </x-secondary-button>
                        </a>
                    @endif
                </div>
                <x-primary-button class="ml-3" id="next-btn">
                    {{ __('Next') }}
                </x-primary-button>
            </div>
        </form>

</div>
