<?php

use App\Http\Controllers\API\V1\ActivateEmployeeController;
use App\Http\Controllers\API\V1\AttachTagsToEmployeesController;
use App\Http\Controllers\API\V1\AuditController;
use App\Http\Controllers\API\V1\BulkUpdateEmployeesDepartmentController;
use App\Http\Controllers\API\V1\BulkUpdateEmployeesDirectManagerController;
use App\Http\Controllers\API\V1\CurrentTenantController;
use App\Http\Controllers\API\V1\DeactivateEmployeeController;
use App\Http\Controllers\API\V1\DepartmentController;
use App\Http\Controllers\API\V1\EmployeeController;
use App\Http\Controllers\API\V1\EmployeeTemplateDownloadController;
use App\Http\Controllers\API\V1\GetCurrentEmployeeController;
use App\Http\Controllers\API\V1\GetCurrentTeamSubscriptionController;
use App\Http\Controllers\API\V1\ImportEmployeeController;
use App\Http\Controllers\API\V1\RoleController;
use App\Http\Controllers\API\V1\SetEmployeePreferredLanguageController;
use App\Http\Controllers\API\V1\TagController;
use App\Http\Controllers\API\V1\TokenController;
use App\Http\Controllers\API\V1\UpdateTenantSettingsController;
use App\Http\Controllers\API\V1\UploadSignedUrlController;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
*/

Route::get('current-employee', GetCurrentEmployeeController::class);
Route::get('tenants/current', CurrentTenantController::class);

Route::put('employees/preferred-language', SetEmployeePreferredLanguageController::class);

Route::middleware('role:start-admin')->group(function () {
    Route::get('roles-list', [RoleController::class, 'index']);
    Route::get('audits', AuditController::class);
    Route::put('settings/update', UpdateTenantSettingsController::class);

    Route::prefix('employees')->group(function () {
        Route::get('', [EmployeeController::class, 'index']);
        Route::get('{employeeId}/show', [EmployeeController::class, 'show']);
        Route::put('{employeeId}/update', [EmployeeController::class, 'update']);
        Route::post('employee/new', [EmployeeController::class, 'store']);

        Route::post('employee/import', ImportEmployeeController::class);
        Route::get('employee/download-template', EmployeeTemplateDownloadController::class);
        Route::put('{employeeId}/activate', ActivateEmployeeController::class);
        Route::put('{employeeId}/deactivate', DeactivateEmployeeController::class);

        Route::put(
            'update-department/{department}',
            BulkUpdateEmployeesDepartmentController::class
        );

        Route::put(
            'update-direct-manager/{manager}',
            BulkUpdateEmployeesDirectManagerController::class
        );

        Route::put('attach-tags', AttachTagsToEmployeesController::class);
    });

    Route::prefix('departments')->group(function () {
        Route::get('', [DepartmentController::class, 'index']);
        Route::get('{departmentId}', [DepartmentController::class, 'show']);
        Route::put('{departmentId}', [DepartmentController::class, 'update']);
        Route::post('', [DepartmentController::class, 'store']);
        Route::delete('{departmentId}', [DepartmentController::class, 'destroy']);
    });

    Route::get('subscription', GetCurrentTeamSubscriptionController::class);
    Route::get('tags', [TagController::class, 'index']);
    Route::post('tags', [TagController::class, 'store']);
    Route::put('tags/{tag}', [TagController::class, 'update']);
    Route::delete('tags/{tag}', [TagController::class, 'destroy']);
});

Route::get('upload-signed-url', UploadSignedUrlController::class)->middleware('auth');

Route::prefix('tokens')
    ->middleware('role:start-developer|start-admin')
    ->group(function () {
        Route::get('', [TokenController::class, 'index']);
        Route::post('', [TokenController::class, 'store']);
        Route::delete('{token}', [TokenController::class, 'destroy']);
    });
