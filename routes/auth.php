<?php

use App\Http\Controllers\Auth\IdentifierFormController;
use App\Http\Controllers\Auth\LanguageController;
use App\Http\Controllers\Auth\LogoutController;
use App\Http\Controllers\Auth\OtpFormController;
use App\Http\Controllers\Auth\ResendOtpFromController;
use App\Http\Controllers\Auth\RevokeCurrentTokenController;
use App\Http\Controllers\Auth\SelectTenantController;
use App\Http\Middleware\CheckOtp;
use Illuminate\Support\Facades\Route;

Route::middleware('guest')->group(function () {
    Route::redirect('/', '/login')->name('login');

    Route::get('/login', [IdentifierFormController::class, 'create'])->name(
        'identifier-form.create'
    );
    Route::post('/login', [IdentifierFormController::class, 'store'])->name(
        'identifier-form.store'
    );

    Route::get('/select-tenants', [SelectTenantController::class, 'create'])->name('select-tenant');
    Route::post('/select-tenants', [SelectTenantController::class, 'store'])->name(
        'select-tenants.select'
    );

    Route::middleware(CheckOtp::class)->group(function () {
        Route::get('/otp', [OtpFormController::class, 'create'])->name('otp.create');
        Route::post('/otp', [OtpFormController::class, 'store'])->name('otp.store');
        Route::post('/resend-otp', ResendOtpFromController::class)->name('resend-otp');
    });
});

Route::get('logout', LogoutController::class)
    ->middleware('auth')
    ->name('logout');

Route::delete('oauth/token', [RevokeCurrentTokenController::class, 'store'])
    ->withoutMiddleware('web')
    ->middleware('auth:api');

Route::get('language/{language}', LanguageController::class)->name('language');
