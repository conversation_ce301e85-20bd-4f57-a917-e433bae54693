<?php

namespace Tests\Datasets;

use App\Models\Department;
use App\Models\Employee;

dataset(
    'full payload',
    fn() => [
        function () {
            $tenant = $this->employee->tenant ?? $this->tenant;

            return [
                'first_name' => fake()->name(),
                'last_name' => fake()->name(),
                'phone' => fake()->numerify('050#######'),
                'email' => fake()->email(),
                'position' => fake()->word(),
                'number' => fake()->numerify('#######'),
                'tenant_id' => $tenant->id,
                'role_ids' => $this->employee->tenant
                    ->rolesBySubscription()
                    ->pluck('uuid')
                    ->toArray(),
                'manager_id' => Employee::factory()->for($tenant)->create()->id,
                'department_id' => Department::factory()->for($tenant)->create()->id,
            ];
        },
    ]
);

dataset('employee payloads', [
    'minimal employee payload' => [
        fn() => [
            'first_name' => fake()->name(),
            'email' => fake()->email(),
            'number' => fake()->numerify('#######'),
            'department_id' => Department::factory()
                ->for($this->tenant)
                ->create()->id,
        ],
    ],
    'full employee payload' => [
        fn() => [
            'first_name' => fake()->name(),
            'last_name' => fake()->name(),
            'phone' => fake()->numerify('050#######'),
            'email' => fake()->email(),
            'position' => fake()->word(),
            'number' => fake()->numerify('#######'),
            'roles' => $this->tenant->rolesBySubscription()->pluck('name')->toArray(),
            'manager_id' => createDefaultEmployee()->id,
            'department_id' => Department::factory()
                ->for($this->tenant)
                ->create()->id,
            'is_active' => fake()->boolean(),
            'preferred_language' => fake()->randomElement(['ar', 'en']),
        ],
    ],
]);

dataset('employee department payloads', [
    'valid department name payload' => fn() => [
        ['department_name' => createDefaultDepartment()->name],
    ],
    'valid department id payload' => fn() => [['department_id' => createDefaultDepartment()->id]],
    'invalid department name payload' => [['department_name' => 'not-existing']],
    'invalid department id payload' => [['department_id' => 'not-existing']],
]);

dataset('get employee cases', [
    'get employee by id' => fn() => createDefaultEmployee()->id,
    'get employee by email' => fn() => createDefaultEmployee()->email,
    'get employee by number' => fn() => createDefaultEmployee()->number,
    'get employee by invalid id' => fn() => 'not-existing',
    'get employee by invalid email' => fn() => 'not-existing',
    'get employee by invalid number' => fn() => 'not-existing',
]);

dataset('payload for excel', function () {
    return [
        fn() => [
            'first_name' => 'John',
            'last_name' => 'Doe',
            'manager_email' => $this->employee->email,
            'phone' => fake()->numerify('050#######'),
            'email' => '<EMAIL>',
            'position' => 'Developer',
            'number' => 'EMP001',
            'department' => 'new',
        ],
    ];
});

dataset('payload for excel without first name', function () {
    return [
        fn() => [
            'last_name' => 'Doe',
            'phone' => fake()->numerify('050#######'),
            'email' => '<EMAIL>',
            'position' => 'Developer',
            'number' => 'EMP001',
            'department' => 'new',
        ],
    ];
});
