<?php

use App\Models\Employee;
use App\Models\Role;
use App\Models\Tenant;
use App\Notifications\TokenExpirationNotification;
use Carbon\Carbon;
use function Pest\Laravel\artisan;
use function Pest\Laravel\travelTo;

it('can send a reminder to users with expiring tokens - to developer if exist', function () {
    Notification::fake();

    travelTo(Carbon::parse('2021-01-01'));

    $tenant = Tenant::factory()->create(['name' => 'token-expiration-reminder']);

    $developer = Employee::factory()
        ->for($tenant)
        ->create()
        ->assignRole(Role::findByName('start-developer'));

    $admin = Employee::factory()
        ->for($tenant)
        ->create()
        ->assignRole(Role::findByName('start-admin'));

    // create a token that expire in 2022-01-01
    $this->actingAsStartAdmin($admin)
        ->postJson('api/v1/tenant/tokens', ['name' => 'new token'])
        ->assertOk();

    // token expires in 2 weeks
    travelTo(Carbon::parse('2021-12-20'));

    artisan('app:token-expiration-reminder')->assertSuccessful();

    Notification::assertSentTo($developer, TokenExpirationNotification::class);

    Notification::assertNotSentTo($admin, TokenExpirationNotification::class);
});

it(
    'can send a reminder to users with expiring tokens - to admin if developers does not exist',
    function () {
        Notification::fake();

        travelTo(Carbon::parse('2021-01-01'));

        $tenant = Tenant::factory()->create(['name' => 'token-expiration-reminder']);

        $admin = Employee::factory()
            ->for($tenant)
            ->create()
            ->assignRole(Role::findByName('start-admin'));

        // create a token that expire in 2022-01-01
        $this->actingAsStartAdmin($admin)
            ->postJson('api/v1/tenant/tokens', ['name' => 'new token'])
            ->assertOk();

        // token expires in 2 weeks
        travelTo(Carbon::parse('2021-12-20'));

        artisan('app:token-expiration-reminder')->assertSuccessful();

        Notification::assertSentTo($admin, TokenExpirationNotification::class);
    }
);

it('can not send a reminder to users with expiring tokens - if token does not exist', function () {
    Notification::fake();

    travelTo(Carbon::parse('2021-01-01'));

    $tenant = Tenant::factory()->create(['name' => 'token-expiration-reminder']);

    $admin = Employee::factory()
        ->for($tenant)
        ->create()
        ->assignRole(Role::findByName('start-admin'));

    // we did not create any token...

    // token expires in 2 weeks
    travelTo(Carbon::parse('2021-12-20'));

    artisan('app:token-expiration-reminder')->assertSuccessful();

    Notification::assertNothingSent();
});

it(
    'can not send a reminder to users with expiring tokens - if token does not expire within 2 weeks',
    function () {
        Notification::fake();

        travelTo(Carbon::parse('2021-01-01'));

        $tenant = Tenant::factory()->create(['name' => 'token-expiration-reminder']);

        $admin = Employee::factory()
            ->for($tenant)
            ->create()
            ->assignRole(Role::findByName('start-admin'));

        // create a token that expire in 2022-01-01
        $this->actingAsStartAdmin($admin)
            ->postJson('api/v1/tenant/tokens', ['name' => 'new token'])
            ->assertOk();

        // token expires in 3 weeks
        travelTo(Carbon::parse('2021-12-05'));

        artisan('app:token-expiration-reminder')->assertSuccessful();

        Notification::assertNothingSent();
    }
);
