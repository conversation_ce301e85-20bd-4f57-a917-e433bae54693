<?php

use App\DTOs\Identifier;
use App\Models\Employee;
use App\Models\Tenant;
use App\Notifications\OneTimePasswordNotification;
use App\Support\LoginSession;

it('can display the email login page', function () {
    /** @see \App\Http\Controllers\Auth\IdentifierFormController */
    $this->get(route('identifier-form.create'))->assertOk();
});

it(
    'logs in with valid email and redirects correctly when multiple tenants exist for the same email',
    function (Identifier $identifier) {
        $tenant1 = Tenant::factory()->create();
        $tenant2 = Tenant::factory()->create();

        Employee::factory()->for($tenant1)->withIdentifier($identifier)->create();
        Employee::factory()->for($tenant2)->withIdentifier($identifier)->create();

        /** @see \App\Http\Controllers\Auth\IdentifierFormController */
        $this->post(route('identifier-form.store'), [
            'identifier' => $identifier->value(),
        ])
            ->assertRedirect(route('select-tenant'))
            ->assertSessionHas([
                LoginSession::IDENTIFIER => $identifier->value(),
            ]);
    }
)->with('identifier');

it('throws validation error when all accounts are inactive', function () {
    $phone = new Identifier('**********');

    $tenant1 = Tenant::factory()->create();
    $tenant2 = Tenant::factory()->create();

    Employee::factory()->for($tenant1)->withIdentifier($phone)->inactive()->create();

    Employee::factory()->for($tenant2)->withIdentifier($phone)->inactive()->create();

    /** @see \App\Http\Controllers\Auth\IdentifierFormController */
    $this->post(route('identifier-form.store'), [
        'identifier' => $phone->value(),
    ])->assertSessionHasErrors('identifier');
});

it('throws validation error when identifier is not in system', function (Identifier $identifier) {
    /** @see \App\Http\Controllers\Auth\IdentifierFormController */
    $this->post(route('identifier-form.store'), [
        'identifier' => $identifier->value(),
    ])->assertSessionHasErrors('identifier');
})->with('identifier');

it('redirect to otp page if identifier is correct', function (Identifier $identifier) {
    Notification::fake();

    $tenant = Tenant::factory()->withDomain($identifier)->create();

    $employee = Employee::factory()->for($tenant)->withIdentifier($identifier)->create();

    /** @see \App\Http\Controllers\Auth\IdentifierFormController */
    $this->post(route('identifier-form.store'), [
        'identifier' => $identifier->value(),
    ])
        ->assertRedirect(route('otp.create'))
        ->assertSessionHas([
            LoginSession::IDENTIFIER => $identifier->value(),
        ]);

    Notification::assertSentTo($employee, OneTimePasswordNotification::class);
})->with('identifier');

it('when tenant allows sms and employee does not have phone, use employee email', function () {
    $identifier = new Identifier(fake()->email());

    Notification::fake();

    $tenant = Tenant::factory()->withDomain($identifier)->allowSms(true)->create();

    $employee = Employee::factory()
        ->for($tenant)
        ->withIdentifier($identifier)
        ->create(['phone' => null]);

    /** @see \App\Http\Controllers\Auth\IdentifierFormController */
    $this->post(route('identifier-form.store'), [
        'identifier' => $identifier->value(),
    ])
        ->assertRedirect(route('otp.create'))
        ->assertSessionHas([
            LoginSession::IDENTIFIER => $identifier->value(),
        ]);

    Notification::assertSentTo($employee, OneTimePasswordNotification::class);
});

it('redirect to external authenticator', function (Identifier $identifier) {
    $tenant = Tenant::factory()->withDomain($identifier)->externalAuthenticator()->create();

    $employee = Employee::factory()
        ->for($tenant)
        ->withIdentifier($identifier)
        ->create(['phone' => null]);

    /** @see \App\Http\Controllers\Auth\IdentifierFormController */
    $this->post(route('identifier-form.store'), [
        'identifier' => $identifier->value(),
    ])
        ->assertRedirect()
        ->assertSessionHas([
            LoginSession::IDENTIFIER => $identifier->value(),
        ]);
})->with('identifier');
