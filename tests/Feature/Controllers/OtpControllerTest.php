<?php

use App\Authenticators\OneTimePasswordAuthenticator;
use App\DTOs\Identifier;
use App\Models\Employee;
use App\Models\Tenant;

it('can display the OTP form', function (Identifier $identifier) {
    $tenant = Tenant::factory()->create();

    Employee::factory()->for($tenant)->withIdentifier($identifier)->create();

    loginSession()->start($identifier->value());

    (new OneTimePasswordAuthenticator())->sendOtp($tenant, loginSession()->resolveIdentifier());

    $this->get(route('otp.create'))->assertOk();
})->with('identifier');

it('can login in with valid OTP', function (Identifier $identifier) {
    $tenant = Tenant::factory()->create();

    Employee::factory()->for($tenant)->withIdentifier($identifier)->create();

    loginSession()->start($identifier->value());

    (new OneTimePasswordAuthenticator())->sendOtp($tenant, loginSession()->resolveIdentifier());

    $this->post(route('otp.store'), [
        'otp' => loginSession()->resolveOtp()->value,
    ])->assertRedirect(config('app.frontend_url'));
})->with('identifier');

it('can not login in with valid OTP', function (Identifier $identifier) {
    $tenant = Tenant::factory()->create();

    Employee::factory()->for($tenant)->withIdentifier($identifier)->create();

    loginSession()->start($identifier->value());

    (new OneTimePasswordAuthenticator())->sendOtp($tenant, loginSession()->resolveIdentifier());

    $this->post(route('otp.store'), ['otp' => fake()->numerify('####')])->assertSessionHasErrors([
        'otp' => __('One time password is wrong, please try again.'),
    ]);
})->with('identifier');

it('redirect to login if otp not sent', function (Identifier $identifier) {
    $tenant = Tenant::factory()->create();

    Employee::factory()->for($tenant)->withIdentifier($identifier)->create();

    loginSession()->start($identifier->value());

    //    (new OneTimePasswordAuthenticator())->sendOtp($tenant, loginSession()->resolveIdentifier());

    $this->post(route('otp.store'), ['otp' => fake()->numerify('####')])->assertRedirectToRoute(
        'identifier-form.create'
    );
})->with('identifier');

it('redirect to login if identifier is not provided', function (Identifier $identifier) {
    $tenant = Tenant::factory()->create();

    Employee::factory()->for($tenant)->withIdentifier($identifier)->create();

    //    loginSession()->start($identifier->value());

    //    (new OneTimePasswordAuthenticator())->sendOtp($tenant, loginSession()->resolveIdentifier());

    $this->post(route('otp.store'), ['otp' => fake()->numerify('####')])->assertRedirectToRoute(
        'identifier-form.create'
    );
})->with('identifier');
