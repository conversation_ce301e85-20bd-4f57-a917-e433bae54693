<?php

use App\Authenticators\OneTimePasswordAuthenticator;
use App\DTOs\Identifier;
use App\Models\Employee;
use App\Models\Tenant;
use App\Notifications\OneTimePasswordNotification;
use App\Support\LoginSession;
use function Pest\Laravel\travelTo;

it('can resend OTP', function (Identifier $identifier) {
    Notification::fake();

    $tenant = Tenant::factory()->create();

    $employee = Employee::factory()->for($tenant)->withIdentifier($identifier)->create();

    loginSession()->start($identifier->value());

    (new OneTimePasswordAuthenticator())->sendOtp($tenant, loginSession()->resolveIdentifier());

    $oldOtp = loginSession()->resolveOtp()->value;
    // after 10 minutes, OTP should be expired, so we can resend it
    travelTo(now()->addMinutes(10));

    $this->post(route('resend-otp'))->assertRedirectToRoute('otp.create');

    app()->forgetInstance(LoginSession::class);

    $newOtp = loginSession()->resolveOtp()->value;

    $this->assertNotEquals($oldOtp, $newOtp);

    Notification::assertSentToTimes($employee, OneTimePasswordNotification::class, 2);
})->with('identifier');

it('can not resend OTP when request new OTP immediately', function (Identifier $identifier) {
    Notification::fake();

    $tenant = Tenant::factory()->create();

    $employee = Employee::factory()->for($tenant)->withIdentifier($identifier)->create();

    loginSession()->start($identifier->value());

    (new OneTimePasswordAuthenticator())->sendOtp($tenant, loginSession()->resolveIdentifier());

    $oldOtp = loginSession()->resolveOtp()->value;

    $this->post(route('resend-otp'))->assertRedirectToRoute('otp.create');

    $newOtp = loginSession()->resolveOtp()->value;

    // we have just sent OTP, so we should not be able to resend it
    $this->assertEquals($oldOtp, $newOtp);

    Notification::assertSentToTimes($employee, OneTimePasswordNotification::class, 1);
})->with('identifier');

it('can send OTP to email then to phone', function () {
    $email = new Identifier(fake()->email());
    $phone = new Identifier(fake()->numerify('050#######'));

    $tenant = Tenant::factory()->allowSms(true)->create();

    Employee::factory()
        ->for($tenant)
        ->create([
            'email' => $email->value(),
            'phone' => $phone->value(),
        ]);

    loginSession()->start($email->value());

    // first, OTP should be sent to email
    (new OneTimePasswordAuthenticator())->sendOtp($tenant, loginSession()->resolveIdentifier());

    // then, OTP should be sent to phone
    $this->post(route('resend-otp', ['identifier' => $phone->value()]))->assertRedirectToRoute(
        'otp.create'
    );

    $this->assertEquals(loginSession()->resolveIdentifier()->value(), $phone->value());
});
