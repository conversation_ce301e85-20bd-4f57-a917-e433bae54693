<?php

use App\DTOs\Identifier;
use App\Models\Employee;
use App\Models\Tenant;

it('displays the select tenant page', function () {
    $identifier = new Identifier(fake()->numerify('050#######'));

    $tenant1 = Tenant::factory()->create();
    $tenant2 = Tenant::factory()->create();

    Employee::factory()->for($tenant1)->withIdentifier($identifier)->create();
    Employee::factory()->for($tenant2)->withIdentifier($identifier)->create();

    loginSession()->start($identifier->value());

    $this->get(route('select-tenant'))->assertOk();
});

it('redirect when there is a single employee', function () {
    $identifier = new Identifier(fake()->numerify('050#######'));

    $tenant1 = Tenant::factory()->create();

    Employee::factory()->for($tenant1)->withIdentifier($identifier)->create();

    loginSession()->start($identifier->value());

    $this->get(route('select-tenant'))->assertRedirectToRoute('identifier-form.create');
});

it('selects a tenant and redirects correctly', function () {
    $identifier = new Identifier(fake()->numerify('050#######'));

    $tenant1 = Tenant::factory()->create();
    $tenant2 = Tenant::factory()->create();

    Employee::factory()->for($tenant1)->withIdentifier($identifier)->create();
    Employee::factory()->for($tenant2)->withIdentifier($identifier)->create();

    loginSession()->start($identifier->value());

    $this->post(route('select-tenants.select'), ['tenant' => $tenant1->id])->assertRedirectToRoute(
        'otp.create'
    );

    expect(loginSession()->resolveTenant()->id)->toBe($tenant1->id);
});

it('shows error when employee is inactive', function () {
    $identifier = new Identifier(fake()->numerify('050#######'));

    $tenant1 = Tenant::factory()->create();
    $tenant2 = Tenant::factory()->create();

    // Create an inactive employee for tenant1
    Employee::factory()->inactive()->for($tenant1)->withIdentifier($identifier)->create();

    // Create an active employee for tenant2
    Employee::factory()->for($tenant2)->withIdentifier($identifier)->create();

    loginSession()->start($identifier->value());

    $this->post(route('select-tenants.select'), ['tenant' => $tenant1->id])->assertSessionHasErrors(
        [
            'email' => __('Your Account is not Active. Please Contact Your Administrator'),
        ]
    );
});
