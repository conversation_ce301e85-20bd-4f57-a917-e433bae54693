<?php

use App\Models\Department;
use App\Models\Employee;
use App\Models\Role;
use App\Models\Tenant;
use Database\Seeders\RolesAndPermissions;
use Laravel\Passport\Passport;

beforeEach(function () {
    $this->employee = Employee::factory()->create();

    Passport::actingAs($this->employee);

    $this->app->make(\Spatie\Permission\PermissionRegistrar::class)->forgetCachedPermissions();

    $this->seed(RolesAndPermissions::class);

    $this->startRole = Role::first();

    $this->employee->assignRole($this->startRole);
});

it('can get the list of departments that belong to their tenant ', function () {
    Department::factory()
        ->count(3)
        ->create([
            'tenant_id' => $this->employee->tenant->id,
            'manager_id' => $this->employee->id,
        ]);

    $this->getJson('api/v1/tenant/departments')->assertOk()->assertJsonCount(3, 'data');
});

it('can create new department ', function () {
    $payload = [
        'name' => fake()->name(),
        'manager_id' => $this->employee->id,
        'tenant_id' => $this->employee->tenant->id,
    ];

    $response = $this->postJson('api/v1/tenant/departments', $payload)
        ->assertOk()
        ->assertJson(['message' => __('The Department has been created successfully.')]);

    $department = Department::firstWhere('name', $payload['name']);

    $this->assertModelExists($department);

    expect($response->json('data'))
        ->id->toBe($department->id)
        ->name->toBe($payload['name']);
});

it('can update department that be long to their tenant ', function () {
    $department = Department::factory()->create([
        'tenant_id' => $this->employee->tenant->id,
        'manager_id' => $this->employee->id,
    ]);

    $payload = [
        'name' => 'new department',
        'manager_id' => $this->employee->id,
    ];

    $response = $this->putJson("api/v1/tenant/departments/{$department['id']}", $payload)
        ->assertOk()
        ->assertJson([
            'message' => __('The department has been updated successfully.'),
        ]);

    $department = Department::firstWhere('name', $payload['name']);

    expect($response->json('data'))
        ->id->toBe($department->id)
        ->name->toBe($payload['name'])
        ->manager->id->toBe($this->employee->id);
});

it('can retrieve details of a single department record', function () {
    $department = Department::factory()->create([
        'tenant_id' => $this->employee->tenant->id,
        'manager_id' => $this->employee->id,
    ]);

    $response = $this->getJson("api/v1/tenant/departments/{$department['id']}")->assertOk();

    expect($response->json('data'))
        ->id->toBe($department->id)
        ->name->toBe($department->name)
        ->manager->id->toBe($this->employee->id);
});

it('only fetch the current tenant departments', function () {
    $anotherTenant = Tenant::factory()->create();

    Department::factory()
        ->count(3)
        ->create([
            'tenant_id' => $this->employee->tenant->id,
        ]);

    Department::factory()
        ->count(3)
        ->create([
            'tenant_id' => $anotherTenant->id,
        ]);

    $this->getJson('api/v1/tenant/departments')->assertOk()->assertJsonCount(3, 'data');
});
describe('Department Delete', function () {
    it('can delete department', function () {
        $department = Department::factory()->create(['tenant_id' => $this->employee->tenant->id]);

        $this->deleteJson("api/v1/tenant/departments/$department->id")
            ->assertOk()
            ->assertJson(['message' => __('The department has been deleted successfully.')]);
    });

    it('cannot delete department that have employees', function () {
        $department = Department::factory()->create(['tenant_id' => $this->employee->tenant->id]);

        Employee::factory()->create([
            'tenant_id' => $this->employee->tenant->id,
            'department_id' => $department->id,
        ]);

        $this->deleteJson("api/v1/tenant/departments/$department->id")
            ->assertStatus(422)
            ->assertJson(['message' => __('Department that has employees cannot be deleted')]);
    });

    it('cannot delete department that have parent', function () {
        $department = Department::factory()->create(['tenant_id' => $this->employee->tenant->id]);

        Department::factory()->create([
            'tenant_id' => $this->employee->tenant->id,
            'parent_id' => $department->id,
        ]);

        $this->deleteJson("api/v1/tenant/departments/$department->id")
            ->assertStatus(422)
            ->assertJson([
                'message' => __('Department that has sub-departments cannot be deleted'),
            ]);
    });

    it('cannot delete non-exists department', function () {
        $this->deleteJson('api/v1/tenant/departments/99')->assertNotFound();
    });

    it('cannot delete department not belong to them', function () {
        $tenant = Tenant::factory()->create();
        $department = Department::factory()->create(['tenant_id' => $tenant->id]);

        $this->deleteJson("api/v1/tenant/departments/$department->id")->assertNotFound();
    });
});
