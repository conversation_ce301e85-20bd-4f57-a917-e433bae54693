<?php

namespace Tests\Feature\Http\Controllers\API\V1;

use App\Models\Department;
use App\Models\Employee;
use App\Models\Tenant;
use OwenIt\Auditing\Models\Audit;

test('fetch audits', function () {
    Tenant::disableAuditing();
    Employee::disableAuditing();
    Department::disableAuditing();

    $tenant = Tenant::factory()->create();

    $employee = Employee::factory()->for($tenant)->create();

    Tenant::enableAuditing();
    Employee::enableAuditing();
    Department::enableAuditing();

    $this->actingAs($employee);

    // 1. Event: Employee creation
    $auditedEmployee = Employee::create([
        'first_name' => 'John',
        'last_name' => 'Doe',
        'email' => '<EMAIL>',
        'number' => 'EMP001',
        'tenant_id' => $tenant->id,
    ]);

    // 2. Event: Employee update
    $auditedEmployee->update(['first_name' => 'Jane']);

    /** @see \App\Http\Controllers\API\V1\AuditController */
    $response = $this->actingAsStartAdmin($employee)->getJson('api/v1/tenant/audits')->assertOk();

    expect($response->json('data.data'))->toHaveCount(2);

    // 1. Event: Employee creation
    expect($response->json('data.data.0.event'))->toBe('created');
    expect($response->json('data.data.0.old_values'))->toBe([]);
    expect($response->json('data.data.0.new_values.id'))->toBe($auditedEmployee->id);

    // 2. Event: Employee update
    expect($response->json('data.data.1.event'))->toBe('updated');
    expect($response->json('data.data.1.old_values.first_name'))->toBe('John');
    expect($response->json('data.data.1.new_values.first_name'))->toBe('Jane');
});

test('fetch audits - search by user', function () {
    Tenant::disableAuditing();
    Employee::disableAuditing();
    Department::disableAuditing();

    $tenant = Tenant::factory()->create();

    $includedEmployee = Employee::factory()->for($tenant)->create();
    $excludedEmployee = Employee::factory()->for($tenant)->create();

    $auditedEmployee = Employee::create([
        'first_name' => 'John',
        'last_name' => 'Doe',
        'email' => '<EMAIL>',
        'number' => 'EMP001',
        'tenant_id' => $tenant->id,
    ]);

    $includedAudit = Audit::create([
        'event' => 'created',
        'auditable_id' => $auditedEmployee->id,
        'auditable_type' => Employee::class,
        'user_id' => $includedEmployee->id,
        'user_type' => Employee::class,
        'tenant_id' => $tenant->id,
    ]);

    $excludedAudit = Audit::create([
        'event' => 'created',
        'auditable_id' => $auditedEmployee->id,
        'auditable_type' => Employee::class,
        'user_id' => $excludedEmployee->id,
        'user_type' => Employee::class,
        'tenant_id' => $tenant->id,
    ]);

    Tenant::enableAuditing();
    Employee::enableAuditing();
    Department::enableAuditing();

    /** @see \App\Http\Controllers\API\V1\AuditController */
    $response = $this->actingAsStartAdmin($includedEmployee)
        ->getJson(
            'api/v1/tenant/audits?' .
                http_build_query(['filter[search]' => $includedEmployee->email])
        )
        ->assertOk();

    expect($response->json('data.data'))->toHaveCount(1);

    expect($response->json('data.data.0.id'))->toBe($includedAudit->id);
    expect($response->json('data.data.0.event'))->toBe('created');
});

test('search filter matches values in new_values and old_values', function () {
    $tenant = Tenant::factory()->create();
    $employee = Employee::factory()->for($tenant)->create();

    $this->actingAs($employee);

    // Create an audit with searchable content in new_values
    $auditWithNewValues = Audit::create([
        'user_type' => Employee::class,
        'user_id' => $employee->id,
        'event' => 'created',
        'auditable_type' => Employee::class,
        'auditable_id' => 1,
        'old_values' => [],
        'new_values' => ['name' => 'SEARCHABLE_TEST_STRING'],
        'tenant_id' => $tenant->id,
    ]);

    // Create an audit with searchable content in old_values
    $auditWithOldValues = Audit::create([
        'user_type' => Employee::class,
        'user_id' => $employee->id,
        'event' => 'updated',
        'auditable_type' => Employee::class,
        'auditable_id' => 1,
        'old_values' => ['name' => 'ANOTHER_SEARCHABLE_STRING'],
        'new_values' => ['name' => 'new name'],
        'tenant_id' => $tenant->id,
    ]);

    // Create an audit without matching content
    $auditWithoutMatch = Audit::create([
        'user_type' => Employee::class,
        'user_id' => $employee->id,
        'event' => 'updated',
        'auditable_type' => Employee::class,
        'auditable_id' => 1,
        'old_values' => ['name' => 'no match'],
        'new_values' => ['name' => 'still no match'],
        'tenant_id' => $tenant->id,
    ]);

    // Test search in new_values
    $response = $this->actingAsStartAdmin($employee)
        ->getJson('api/v1/tenant/audits?filter[search]=SEARCHABLE_TEST_STRING')
        ->assertOk();

    expect($response->json('data.data'))->toHaveCount(1);
    expect($response->json('data.data.0.id'))->toBe($auditWithNewValues->id);

    // Test search in old_values
    $response = $this->actingAsStartAdmin($employee)
        ->getJson('api/v1/tenant/audits?filter[search]=ANOTHER_SEARCHABLE_STRING')
        ->assertOk();

    expect($response->json('data.data'))->toHaveCount(1);
    expect($response->json('data.data.0.id'))->toBe($auditWithOldValues->id);

    // Test search with no matches
    $response = $this->actingAsStartAdmin($employee)
        ->getJson('api/v1/tenant/audits?filter[search]=NO_MATCH_STRING')
        ->assertOk();

    expect($response->json('data.data'))->toHaveCount(0);

    // Test partial string match
    $response = $this->actingAsStartAdmin($employee)
        ->getJson('api/v1/tenant/audits?filter[search]=SEARCHABLE')
        ->assertOk();

    expect($response->json('data.data'))->toHaveCount(2);
    expect($response->json('data.data.0.id'))->toBe($auditWithNewValues->id);
    expect($response->json('data.data.1.id'))->toBe($auditWithOldValues->id);
});
