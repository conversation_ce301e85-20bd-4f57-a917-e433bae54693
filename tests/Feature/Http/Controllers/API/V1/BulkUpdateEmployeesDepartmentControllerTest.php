<?php

test('update departments of employees - all', function () {
    $oldDepartment = createDefaultDepartment();

    $employee = createDefaultEmployee([
        'department_id' => $oldDepartment,
    ]);

    $newDepartment = createDefaultDepartment();

    /** @see \App\Http\Controllers\API\V1\BulkUpdateEmployeesDepartmentController */
    $this->actingAsStartAdmin()
        ->put("api/v1/tenant/employees/update-department/$newDepartment->id", [
            'all' => true,
        ])
        ->assertOk();

    $employee->refresh();

    expect($employee->department_id)->toBe($newDepartment->id);
});

test('update departments of employees - employees ids', function () {
    $oldDepartment = createDefaultDepartment();

    $search = 'abc';

    $employee = createDefaultEmployee([
        'first_name' => $search,
        'department_id' => $oldDepartment,
    ]);

    $excludedEmployee = createDefaultEmployee([
        'first_name' => 'def',
        'department_id' => $oldDepartment,
    ]);

    $newDepartment = createDefaultDepartment();

    /** @see \App\Http\Controllers\API\V1\BulkUpdateEmployeesDepartmentController */
    $this->actingAsStartAdmin()
        ->put("api/v1/tenant/employees/update-department/$newDepartment->id", [
            'employees' => [$employee->id],
        ])
        ->assertOk();

    $employee->refresh();
    $excludedEmployee->refresh();

    expect($employee->department_id)
        ->toBe($newDepartment->id)
        ->and($excludedEmployee->department_id)
        ->toBe($oldDepartment->id);
});

test('update departments of employees - search', function () {
    $oldDepartment = createDefaultDepartment();

    $search = 'abc';

    $employee = createDefaultEmployee([
        'first_name' => $search,
        'department_id' => $oldDepartment,
    ]);

    $excludedEmployee = createDefaultEmployee([
        'first_name' => 'def',
        'department_id' => $oldDepartment,
    ]);

    $newDepartment = createDefaultDepartment();

    /** @see \App\Http\Controllers\API\V1\BulkUpdateEmployeesDepartmentController */
    $this->actingAsStartAdmin()
        ->put("api/v1/tenant/employees/update-department/$newDepartment->id", [
            'all' => true,
            'filter' => [
                'search' => $search,
            ],
        ])
        ->assertOk();

    $employee->refresh();
    $excludedEmployee->refresh();

    expect($employee->department_id)
        ->toBe($newDepartment->id)
        ->and($excludedEmployee->department_id)
        ->toBe($oldDepartment->id);
});
