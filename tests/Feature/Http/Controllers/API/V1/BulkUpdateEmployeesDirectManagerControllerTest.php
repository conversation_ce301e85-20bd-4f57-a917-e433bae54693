<?php

test('update direct manager of employees - all', function () {
    $oldManager = createDefaultEmployee();

    $employee = createDefaultEmployee([
        'manager_id' => $oldManager->id,
    ]);

    $newManager = createDefaultEmployee();

    /** @see \App\Http\Controllers\API\V1\BulkUpdateEmployeesDirectManagerController */
    $this->actingAsStartAdmin()->put(
        "api/v1/tenant/employees/update-direct-manager/$newManager->id",
        [
            'all' => true,
        ]
    );

    $employee->refresh();

    expect($employee->manager_id)->toBe($newManager->id);
});

test('update direct manager of employees - employees ids', function () {
    $oldManager = createDefaultEmployee();

    $search = 'abc';

    $employee = createDefaultEmployee([
        'first_name' => $search,
        'manager_id' => $oldManager->id,
    ]);

    $excludedEmployee = createDefaultEmployee([
        'first_name' => 'def',
        'manager_id' => $oldManager->id,
    ]);

    $newManager = createDefaultEmployee();

    /** @see \App\Http\Controllers\API\V1\BulkUpdateEmployeesDirectManagerController */
    $this->actingAsStartAdmin()->put(
        "api/v1/tenant/employees/update-direct-manager/$newManager->id",
        [
            'employees' => [$employee->id],
        ]
    );

    $employee->refresh();
    $excludedEmployee->refresh();

    expect($employee->manager_id)
        ->toBe($newManager->id)
        ->and($excludedEmployee->manager_id)
        ->toBe($oldManager->id);
});

test('update direct manager of employees - search', function () {
    $oldManager = createDefaultEmployee();

    $search = 'abc';

    $employee = createDefaultEmployee([
        'first_name' => $search,
        'manager_id' => $oldManager->id,
    ]);

    $excludedEmployee = createDefaultEmployee([
        'first_name' => 'def',
        'manager_id' => $oldManager->id,
    ]);

    $newManager = createDefaultEmployee();

    /** @see \App\Http\Controllers\API\V1\BulkUpdateEmployeesDirectManagerController */
    $this->actingAsStartAdmin()->put(
        "api/v1/tenant/employees/update-direct-manager/$newManager->id",
        [
            'all' => true,
            'filter' => [
                'search' => $search,
            ],
        ]
    );

    $employee->refresh();
    $excludedEmployee->refresh();

    expect($employee->manager_id)
        ->toBe($newManager->id)
        ->and($excludedEmployee->manager_id)
        ->toBe($oldManager->id);
});
