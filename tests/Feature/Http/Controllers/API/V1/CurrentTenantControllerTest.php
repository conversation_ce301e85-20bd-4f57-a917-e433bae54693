<?php

use App\Models\Employee;
use App\Models\Tenant;

test('works', function () {
    $employee = Employee::factory()->for(Tenant::factory())->create();

    /** @see \App\Http\Controllers\API\V1\CurrentTenantController */
    $this->actingAsStartAdmin($employee)
        ->getJson('api/v1/tenant/tenants/current')
        ->assertOk()
        ->assertJson([
            'data' => [
                'id' => $employee->tenant->id,
                'name' => $employee->tenant->name,
            ],
        ]);
});
