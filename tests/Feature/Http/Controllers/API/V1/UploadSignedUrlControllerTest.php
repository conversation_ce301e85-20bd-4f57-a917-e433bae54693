<?php

use App\Enums\Folder;
use Illuminate\Testing\Fluent\AssertableJson;

test('upload signed url', function (Folder $folder) {
    $this->actingAsStartAdmin()
        ->getJson(
            'api/v1/tenant/upload-signed-url?' .
                http_build_query([
                    'display_name' => fake()->word(),
                    'folder' => $folder->value,
                ])
        )
        ->assertOk()
        ->assertJson(
            fn(AssertableJson $json) => $json->has('data.url')->has('data.storage_name')->etc()
        );
})->with(Folder::cases());
