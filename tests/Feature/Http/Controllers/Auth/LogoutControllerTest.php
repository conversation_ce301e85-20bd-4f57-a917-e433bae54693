<?php

use App\Models\Employee;
use App\Models\Passport\Client;
use Lara<PERSON>\Passport\Token;

test('logout without browser_id', function () {
    $employee = Employee::factory()->create();
    $this->actingAs($employee, 'web');

    // Create a client and token
    $client = Client::create([
        'id' => Str::uuid(),
        'name' => 'Test Client',
        'redirect' => 'http://localhost',
        'personal_access_client' => false,
        'password_client' => false,
        'revoked' => false,
    ]);

    $token = Token::create([
        'id' => Str::uuid(),
        'user_id' => $employee->id,
        'client_id' => $client->id,
        'name' => 'Test Token',
        'scopes' => [],
        'revoked' => false,
    ]);

    // Logout without browser_id
    $this->get(route('logout'))->assertRedirect(route('identifier-form.create'));

    // Assert user is logged out
    $this->assertGuest('web');

    // Token should not be revoked because browser_id was not provided
    expect(Token::find($token->id)->revoked)->toBeFalse();
});

test('logout with browser_id revokes tokens', function () {
    $employee = Employee::factory()->create();
    $this->actingAs($employee, 'web');

    // Create a client and token
    $client = Client::create([
        'id' => Str::uuid(),
        'name' => 'Test Client',
        'redirect' => 'http://localhost',
        'personal_access_client' => false,
        'password_client' => false,
        'revoked' => false,
    ]);

    $token = Token::create([
        'id' => Str::uuid(),
        'user_id' => $employee->id,
        'client_id' => $client->id,
        'name' => 'Test Token',
        'scopes' => [],
        'revoked' => false,
    ]);

    // Logout with browser_id
    $this->get(route('logout', ['browser_id' => 'test-browser-id']))->assertRedirect(
        route('identifier-form.create')
    );

    // Assert user is logged out
    $this->assertGuest('web');

    // Token should be revoked because browser_id was provided
    expect(Token::find($token->id)->revoked)->toBeTrue();
});
