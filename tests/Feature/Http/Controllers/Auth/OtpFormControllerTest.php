<?php

use App\DTOs\Identifier;
use App\Models\Employee;
use App\Models\Otp;
use App\Models\Tenant;
use Inertia\Testing\AssertableInertia;

test('redirect to identifier page if no identifier in session', function () {
    /** @see \App\Http\Controllers\Auth\OtpFormController */
    $this->get(route('otp.create'))->assertRedirect(route('identifier-form.create'));
});

test('display otp page', function (Identifier $identifier) {
    $tenant = Tenant::factory()->withDomain($identifier)->create();

    $employee = Employee::factory()->for($tenant)->withIdentifier($identifier)->create();

    loginSession()->start($identifier->value());
    Otp::factory()->identifier($identifier)->for($employee)->for($tenant)->create();

    /** @see \App\Http\Controllers\Auth\OtpFormController */

    $this->get(route('otp.create'))->assertOk()->assertInertia(
        fn(AssertableInertia $assert) => $assert
            ->component('Login/OtpForm')
            ->where('identifier', [
                'type' => $identifier->type()->value,
                'value' => $identifier->masked(),
            ])
            ->where('secondsToExpire', fn($value) => $value > 115 && $value < 125) // 2 minutes
            ->where('allowSms', $tenant->allow_sms)
    );
})->with('identifier');

test('invalid otp - wrong otp', function (Identifier $identifier) {
    $tenant = Tenant::factory()->withDomain($identifier)->create();

    $employee = Employee::factory()->for($tenant)->withIdentifier($identifier)->create();

    loginSession()->start($identifier->value());
    $otp = Otp::factory()
        ->identifier($identifier)
        ->for($employee)
        ->for($tenant)
        ->create([
            'value' => '4321',
        ]);

    /** @see \App\Http\Controllers\Auth\OtpFormController */
    $this->post(route('otp.store'), ['otp' => '1234'])->assertInvalid([
        'otp' => __('One time password is wrong, please try again.'),
    ]);

    expect($otp->refresh()->tries)->toBe(1);
})->with('identifier');

test('invalid otp - exceeded tries', function (Identifier $identifier) {
    $tenant = Tenant::factory()->withDomain($identifier)->create();

    $employee = Employee::factory()->for($tenant)->withIdentifier($identifier)->create();

    loginSession()->start($identifier->value());
    $otp = Otp::factory()
        ->identifier($identifier)
        ->for($employee)
        ->for($tenant)
        ->create([
            'tries' => Otp::ALLOWED_NUMBER_OF_TRIES,
        ]);

    /** @see \App\Http\Controllers\Auth\OtpFormController */
    $this->post(route('otp.store'), ['otp' => '1234'])->assertInvalid([
        'otp' => __('Your session is expired, we sent a new code, please try again.'),
    ]);

    expect($otp->refresh()->tries)->toBe(Otp::ALLOWED_NUMBER_OF_TRIES);
})->with('identifier');

test('valid otp', function (Identifier $identifier) {
    $tenant = Tenant::factory()->withDomain($identifier)->create();

    $employee = Employee::factory()->for($tenant)->withIdentifier($identifier)->create();

    loginSession()->start($identifier->value());

    $otp = Otp::factory()->identifier($identifier)->for($employee)->for($tenant)->create();

    /** @see \App\Http\Controllers\Auth\OtpFormController */
    $this->post(route('otp.store'), ['otp' => $otp->value])->assertRedirect(
        config('app.frontend_url')
    );

    expect($otp->refresh()->tries)
        ->toBe(0)
        ->and($otp->refresh()->used_at)
        ->not->toBeNull()
        ->and(loginSession()->rawIdentifier())
        ->toBeNull();
})->with('identifier');
