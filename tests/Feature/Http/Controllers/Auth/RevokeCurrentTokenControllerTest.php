<?php

use App\Models\Employee;
use App\Models\Passport\Client;
use Laravel\Passport\Passport;
use Laravel\Passport\Token;

test('revoke current token', function () {
    $employee = Employee::factory()->create();

    Passport::actingAs($employee);

    $client = Client::create([
        'id' => Str::uuid(),
        'name' => 'Test Client',
        'redirect' => 'http://localhost',
        'personal_access_client' => false,
        'password_client' => false,
        'revoked' => false,
    ]);

    $token = Token::create([
        'id' => Str::uuid(),
        'user_id' => auth()->id(),
        'client_id' => $client->id,
        'name' => 'Test Token',
        'scopes' => [],
        'revoked' => false,
    ]);

    auth()->user()->withAccessToken($token);

    $this->delete('oauth/token')->assertOk();

    expect(Token::find($token->id)->revoked)->toBeTrue();
});

test('unauthenticated user cannot revoke token', function () {
    $this->delete('oauth/token')->assertRedirect();
});
