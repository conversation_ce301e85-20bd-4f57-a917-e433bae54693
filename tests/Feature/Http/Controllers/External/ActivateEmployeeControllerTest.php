<?php

it('can activate employee', function (array $payload) {
    $this->actingAsTenant()
        ->put<PERSON><PERSON>("api/v1/external/employees/{$payload['identifier']}/activate")
        ->assertOk()
        ->assertJson(['message' => 'Employee activated successfully']);

    expect($payload['employee']->refresh()->is_active)->toBe(1);
})->with([
    'get employee by id' => fn() => [
        'employee' => ($employee = createDefaultEmployee(['is_active' => false])),
        'identifier' => $employee->id,
    ],
    'get employee by email' => fn() => [
        'employee' => ($employee = createDefaultEmployee(['is_active' => false])),
        'identifier' => $employee->email,
    ],
    'get employee by number' => fn() => [
        'employee' => ($employee = createDefaultEmployee(['is_active' => false])),
        'identifier' => $employee->number,
    ],
]);
