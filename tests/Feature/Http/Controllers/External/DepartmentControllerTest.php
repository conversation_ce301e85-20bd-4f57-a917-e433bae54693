<?php

use App\Models\Department;
use App\Models\Employee;
use App\Models\Tenant;
use Laravel\Sanctum\Sanctum;

beforeEach(function () {
    $this->tenant = Tenant::factory()->create();
    Sanctum::actingAs($this->tenant);
});

describe('Department Creation', function () {
    it('can create new department with name', function () {
        $payload = ['name' => 'Department'];

        $response = $this->postJson('api/v1/external/departments', $payload)
            ->assertOk()
            ->assertJson(['message' => 'The Department has been created successfully.']);

        $department = Department::firstWhere('name', $payload['name']);

        $this->assertModelExists($department);

        expect($response->json('data'))
            ->id->toBe($department->id)
            ->name->toBe($payload['name']);
    });

    it('can create new department with parent name', function () {
        $department = Department::factory()->create(['tenant_id' => $this->tenant->id]);

        $payload = ['name' => 'Department', 'parent_name' => $department->name];

        $response = $this->postJson('api/v1/external/departments', $payload)
            ->assertOk()
            ->assertJson(['message' => 'The Department has been created successfully.']);

        $department = Department::firstWhere('name', $payload['name']);

        $this->assertModelExists($department);

        expect($response->json('data'))
            ->id->toBe($department->id)
            ->name->toBe($payload['name'])
            ->parent->name->toBe($payload['parent_name']);
    });

    it('can create new department with manager email', function () {
        $employee = Employee::factory()->create(['tenant_id' => $this->tenant->id]);

        Department::factory()->create([
            'tenant_id' => $this->tenant->id,
            'manager_id' => $employee->id,
        ]);

        $payload = ['name' => 'Department', 'manager_email' => $employee->email];

        $response = $this->postJson('api/v1/external/departments', $payload)
            ->assertOk()
            ->assertJson(['message' => 'The Department has been created successfully.']);

        $department = Department::firstWhere('name', $payload['name']);

        $this->assertModelExists($department);

        expect($response->json('data'))
            ->id->toBe($department->id)
            ->name->toBe($payload['name'])
            ->manager->email->toBe($employee->email);
    });

    it('can create new department with manager id', function () {
        $employee = Employee::factory()->create(['tenant_id' => $this->tenant->id]);

        $payload = ['name' => 'Department', 'manager_id' => $employee->id];

        $response = $this->postJson('api/v1/external/departments', $payload)
            ->assertOk()
            ->assertJson(['message' => 'The Department has been created successfully.']);

        $department = Department::firstWhere('name', $payload['name']);

        $this->assertModelExists($department);

        expect($response->json('data'))
            ->id->toBe($department->id)
            ->name->toBe($payload['name'])
            ->manager->id->toBe($employee->id);
    });

    it('can create new department with parent id', function () {
        $createdDepartment = Department::factory()->create(['tenant_id' => $this->tenant->id]);

        $payload = ['name' => 'Department', 'parent_id' => $createdDepartment->id];

        $response = $this->postJson('api/v1/external/departments', $payload)
            ->assertOk()
            ->assertJson(['message' => 'The Department has been created successfully.']);

        $department = Department::firstWhere('name', $payload['name']);

        $this->assertModelExists($department);

        expect($response->json('data'))
            ->id->toBe($department->id)
            ->name->toBe($payload['name'])
            ->parent->id->toBe($createdDepartment->id);
    });

    it('can not create new department with invalid parent name', function () {
        $payload = ['name' => 'Department', 'parent_name' => 'name'];

        $this->postJson('api/v1/external/departments', $payload)
            ->assertJsonValidationErrors('parent_name')
            ->assertStatus(422)
            ->assertJson(['message' => 'parent department does not exist']);
    });

    it('can not create new department with invalid manager email', function () {
        $payload = ['name' => 'Department', 'manager_email' => '<EMAIL>'];

        $this->postJson('api/v1/external/departments', $payload)->assertJsonValidationErrors([
            'manager_email',
        ]);
    });

    it('can not create new department with no data', function () {
        $payload = [];

        $this->postJson('api/v1/external/departments', $payload)->assertJsonValidationErrors([
            'name',
        ]);
    });

    it('can create new department with all data', function () {
        $employee = Employee::factory()->create(['tenant_id' => $this->tenant->id]);

        $department = Department::factory()->create([
            'tenant_id' => $this->tenant->id,
            'manager_id' => $employee->id,
        ]);

        $payload = [
            'name' => 'name',
            'manager_id' => $employee->id,
            'manager_email' => $employee->email,
            'parent_id' => $department->id,
            'parent_name' => $department->name,
        ];

        $response = $this->postJson('api/v1/external/departments', $payload)
            ->assertOk()
            ->assertJson(['message' => 'The Department has been created successfully.']);

        $newDepartment = Department::firstWhere('name', $payload['name']);

        $this->assertModelExists($newDepartment);

        expect($response->json('data'))
            ->id->toBe($newDepartment->id)
            ->name->toBe($payload['name'])
            ->manager->id->toBe($employee->id)
            ->parent->id->toBe($department->id);
    });
});

describe('Department Update', function () {
    it('can update department name', function () {
        $department = Department::factory()->create(['tenant_id' => $this->tenant->id]);

        $payload = ['name' => 'new name'];

        $response = $this->putJson("api/v1/external/departments/$department->id", $payload)
            ->assertOk()
            ->assertJson(['message' => 'department updated successfully']);

        $department = Department::firstWhere('name', $payload['name']);

        expect($response->json('data'))
            ->id->toBe($department->id)
            ->name->toBe($payload['name']);
    });

    it('can update department with manager email', function () {
        $employee = Employee::factory()->create(['tenant_id' => $this->tenant->id]);

        $department = Department::factory()->create([
            'tenant_id' => $this->tenant->id,
            'manager_id' => $employee->id,
        ]);

        $employee->update(['email' => '<EMAIL>']);

        $employee->fresh();

        $payload = ['manager_email' => $employee->email];

        $response = $this->putJson("api/v1/external/departments/$department->id", $payload)
            ->assertOk()
            ->assertJson(['message' => 'department updated successfully']);

        $department = Department::firstWhere('name', $department['name']);

        expect($response->json('data'))
            ->id->toBe($department->id)
            ->name->toBe($department['name'])
            ->manager->email->toBe($payload['manager_email']);
    });

    it('can update department with manager id', function () {
        $employee = Employee::factory()->create(['tenant_id' => $this->tenant->id]);

        $department = Department::factory()->create([
            'tenant_id' => $this->tenant->id,
            'manager_id' => $employee->id,
        ]);

        $newEmployee = Employee::factory()->create(['tenant_id' => $this->tenant->id]);

        $payload = ['manager_id' => $newEmployee->id];

        $response = $this->putJson("api/v1/external/departments/$department->id", $payload)
            ->assertOk()
            ->assertJson(['message' => 'department updated successfully']);

        $department = Department::firstWhere('name', $department['name']);

        expect($response->json('data'))
            ->id->toBe($department->id)
            ->name->toBe($department['name'])
            ->manager->id->toBe($payload['manager_id']);
    });

    it('can update department with parent name', function () {
        $department = Department::factory()->create(['tenant_id' => $this->tenant->id]);

        $newDepartment = Department::factory()->create(['tenant_id' => $this->tenant->id]);

        $payload = ['parent_name' => $newDepartment->name];

        $response = $this->putJson("api/v1/external/departments/$department->id", $payload)
            ->assertOk()
            ->assertJson(['message' => 'department updated successfully']);

        $department = Department::firstWhere('name', $department['name']);

        expect($response->json('data'))
            ->id->toBe($department->id)
            ->name->toBe($department['name'])
            ->parent->name->toBe($payload['parent_name']);
    });

    it('can update department with parent id', function () {
        $department = Department::factory()->create(['tenant_id' => $this->tenant->id]);

        $newDepartment = Department::factory()->create(['tenant_id' => $this->tenant->id]);

        $payload = ['parent_id' => $newDepartment->id];

        $response = $this->putJson("api/v1/external/departments/$department->id", $payload)
            ->assertOk()
            ->assertJson(['message' => 'department updated successfully']);

        $department = Department::firstWhere('name', $department['name']);

        expect($response->json('data'))
            ->id->toBe($department->id)
            ->name->toBe($department['name'])
            ->parent->id->toBe($payload['parent_id']);
    });

    it('can not update department with invalid parent name', function () {
        $department = Department::factory()->create(['tenant_id' => $this->tenant->id]);

        $payload = ['parent_name' => 'name'];

        $this->putJson(
            "api/v1/external/departments/$department->id",
            $payload
        )->assertJsonValidationErrors(['parent_name']);
    });

    it('can not update department with invalid manager email', function () {
        $department = Department::factory()->create(['tenant_id' => $this->tenant->id]);

        $payload = ['manager_email' => '<EMAIL>'];

        $this->putJson(
            "api/v1/external/departments/$department->id",
            $payload
        )->assertJsonValidationErrors(['manager_email']);
    });

    it('can  create new department with all data', function () {
        $employee = Employee::factory()->create(['tenant_id' => $this->tenant->id]);

        $department = Department::factory()->create([
            'tenant_id' => $this->tenant->id,
            'manager_id' => $employee->id,
        ]);

        $payload = [
            'name' => 'name',
            'manager_id' => $employee->id,
            'manager_email' => $employee->email,
            'parent_id' => $department->id,
            'parent_name' => $department->name,
        ];

        $response = $this->postJson('api/v1/external/departments', $payload)
            ->assertOk()
            ->assertJson(['message' => 'The Department has been created successfully.']);

        $newDepartment = Department::firstWhere('name', $payload['name']);

        $this->assertModelExists($newDepartment);

        expect($response->json('data'))
            ->id->toBe($newDepartment->id)
            ->name->toBe($payload['name'])
            ->manager->id->toBe($employee->id)
            ->parent->id->toBe($department->id);
    });
});

describe('Department Deleting', function () {
    it('can delete department', function () {
        $department = Department::factory()->create(['tenant_id' => $this->tenant->id]);

        $this->deleteJson("api/v1/external/departments/$department->id")
            ->assertOk()
            ->assertJson(['message' => 'The department has been deleted successfully.']);
    });

    it('cannot delete department that have employees', function () {
        $department = Department::factory()->create(['tenant_id' => $this->tenant->id]);

        Employee::factory()->create([
            'tenant_id' => $this->tenant->id,
            'department_id' => $department->id,
        ]);

        $this->deleteJson("api/v1/external/departments/$department->id")
            ->assertStatus(422)
            ->assertJson(['message' => 'Department that has employees cannot be deleted']);
    });

    it('cannot delete department that have parent', function () {
        $department = Department::factory()->create(['tenant_id' => $this->tenant->id]);

        Department::factory()->create([
            'tenant_id' => $this->tenant->id,
            'parent_id' => $department->id,
        ]);

        $this->deleteJson("api/v1/external/departments/$department->id")
            ->assertStatus(422)
            ->assertJson(['message' => 'Department that has sub-departments cannot be deleted']);
    });

    it('cannot delete non-exists department', function () {
        $this->deleteJson('api/v1/external/departments/99')->assertNotFound();
    });

    it('cannot delete department not belong to them', function () {
        $tenant = Tenant::factory()->create();
        $department = Department::factory()->create(['tenant_id' => $tenant->id]);

        $this->deleteJson("api/v1/external/departments/$department->id")->assertNotFound();
    });
});

describe('Department Show', function () {
    it('can show single department data', function () {
        $department = Department::factory()->create([
            'tenant_id' => $this->tenant->id,
        ]);

        $response = $this->getJson("api/v1/external/departments/$department->id")->assertOk();

        expect($response->json('data'))
            ->id->toBe($department->id)
            ->name->toBe($department->name);
    });

    it('can not  show non-exists department ', function () {
        $this->getJson('api/v1/external/departments/100')->assertNotFound();
    });
    it('can not  department not belong to them ', function () {
        $tenant = Tenant::factory()->create();
        $department = Department::factory()->create(['tenant_id' => $tenant->id]);

        $this->getJson("api/v1/external/departments/$department->id")->assertNotFound();
    });
});

describe('Department list', function () {
    it('can get department list', function () {
        Department::factory()
            ->count(5)
            ->create(['tenant_id' => $this->tenant->id]);

        $this->getJson('api/v1/external/departments')->assertOk()->assertJsonCount(5, 'data');
    });
});
