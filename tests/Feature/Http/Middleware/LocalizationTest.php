<?php

use App\Enums\EmployeePreferredLanguage;
use App\Models\Employee;
use App\Models\Role;
use Database\Seeders\RolesAndPermissions;
use Laravel\Passport\Passport;

test('language follows the employee\'s language', function (EmployeePreferredLanguage $language) {
    $employee = Employee::factory()->create([
        'preferred_language' => $language->value,
    ]);

    Passport::actingAs($employee);

    $this->seed(RolesAndPermissions::class);

    $this->startRole = Role::first();

    $employee->assignRole($this->startRole);

    // a random request is selected here for testing purposes
    $this->postJson(
        'api/v1/tenant/departments',
        ['name' => 'Department'],
        ['Accept-Language' => null]
    )
        ->assertOk()
        ->assertJson([
            'message' => match ($employee->preferred_language) {
                EmployeePreferredLanguage::Arabic => 'تم إنشاء القسم بنجاح.',
                EmployeePreferredLanguage::English
                    => 'The Department has been created successfully.',
            },
        ]);
})->with([EmployeePreferredLanguage::Arabic, EmployeePreferredLanguage::English]);

test('Accept-Language will override employee\'s language', function () {
    $employeeLanguage = EmployeePreferredLanguage::English;

    $employee = Employee::factory()->create([
        'preferred_language' => $employeeLanguage->value,
    ]);

    Passport::actingAs($employee);

    $this->seed(RolesAndPermissions::class);

    $this->startRole = Role::first();

    $employee->assignRole($this->startRole);

    // a random request is selected here for testing purposes
    $this->postJson(
        'api/v1/tenant/departments',
        ['name' => 'Department'],
        ['Accept-Language' => 'ar']
    )
        ->assertOk()
        ->assertJson([
            'message' => 'تم إنشاء القسم بنجاح.',
        ]);
});
