<?php

use App\Http\Middleware\SetBrowserId;
use Illuminate\Support\Str;

test('sets browser id cookie when visiting login page', function () {
    config(['app.global_cookie_domain' => '.example.com']);

    $response = $this->get('/login');

    $cookies = $response->headers->getCookies();

    /** @var \Symfony\Component\HttpFoundation\Cookie $bidCookie */
    $bidCookie = collect($cookies)->first(fn($cookie) => $cookie->getName() === SetBrowserId::BID);

    expect($bidCookie)
        ->not->toBeNull('BID cookie not found in response')
        ->and(Str::isUuid($bidCookie->getValue()))
        ->toBeTrue('BID cookie value is not a valid UUID')
        ->and($bidCookie->getDomain())
        ->toBe('.example.com');
});

test('does not reset browser id cookie on subsequent requests', function () {
    // First request - should set the BID cookie
    $firstResponse = $this->get('/login');

    /** @var \Symfony\Component\HttpFoundation\Cookie $firstBidCookie */
    $firstBidCookie = collect($firstResponse->headers->getCookies())->first(
        fn($cookie) => $cookie->getName() === SetBrowserId::BID
    );

    // Store the first BID value
    $firstBidValue = $firstBidCookie->getValue();

    // Second request - should not change the BID cookie
    $secondResponse = $this->withCookie(SetBrowserId::BID, $firstBidValue)->get('/login');

    /** @var \Symfony\Component\HttpFoundation\Cookie $secondBidCookie */
    $secondBidCookie = collect($secondResponse->headers->getCookies())->first(
        fn($cookie) => $cookie->getName() === SetBrowserId::BID
    );

    // The BID value should remain the same
    expect($secondBidCookie->getValue())->toBe($firstBidValue);
});
