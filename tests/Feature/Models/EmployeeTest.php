<?php

use App\Models\Department;
use App\Models\Employee;
use App\Models\Tenant;

beforeEach(function () {
    $this->tenant = Tenant::factory()->create();
});

test('can assign an employee to direct manager', function () {
    $manager = Employee::factory()
        ->for($this->tenant)
        ->create();

    $employee = Employee::factory()
        ->for($this->tenant)
        ->create(['manager_id' => $manager->id]);

    expect($employee->manager->id)->toBe($manager->id);
});

test('can assign an employee to manager via department', function () {
    $manager = Employee::factory()
        ->for($this->tenant)
        ->create();

    $department = Department::factory()
        ->for($this->tenant)
        ->create(['manager_id' => $manager->id]);

    $employee = Employee::factory()
        ->for($this->tenant)
        ->create(['department_id' => $department->id]);

    expect($employee->manager->id)->toBe($manager->id);
});

test(
    'when an employee assigned to direct manager and via department, direct manager will be current manager',
    function () {
        $departmentManager = Employee::factory()
            ->for($this->tenant)
            ->create();
        $directManager = Employee::factory()
            ->for($this->tenant)
            ->create();

        $department = Department::factory()
            ->for($this->tenant)
            ->create(['manager_id' => $departmentManager->id]);

        $employee = Employee::factory()
            ->for($this->tenant)
            ->create([
                'manager_id' => $directManager->id,
                'department_id' => $department->id,
            ]);

        expect($employee->manager->id)->toBe($directManager->id);
    }
);
