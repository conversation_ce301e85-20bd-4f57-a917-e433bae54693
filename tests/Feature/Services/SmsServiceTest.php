<?php

use App\DTOs\Identifier;
use App\Services\SmsService;

test('does not send a message when identifier is not a number', function () {
    Http::fake();

    $identifier = new Identifier(fake()->email());
    $message = fake()->sentence();

    SmsService::send($identifier, $message);

    Http::assertNothingSent();
})->throws(InvalidArgumentException::class, 'Only phone identifiers are supported');

test('send a message', function () {
    Http::fake();

    $identifier = new Identifier(fake()->email());
    $message = fake()->sentence();

    config(['services.sms.url' => 'https://example.com']);
    config(['services.sms.secret' => 'secret']);

    SmsService::send($identifier, $message);

    Http::assertSent(function (Request $request) use ($message, $identifier) {
        return $request->url() == 'https://example.com' &&
            $request['recipient'] == $identifier->value() &&
            $request['body'] == $message;
    });
})->throws(InvalidArgumentException::class, 'Only phone identifiers are supported');
