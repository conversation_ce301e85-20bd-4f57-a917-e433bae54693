<?php

namespace Tests;

use App\Enums\SoftwareCode;
use App\Enums\SoftwarePackageCode;
use App\Models\Employee;
use App\Models\Role;
use App\Models\Subscription;
use App\Models\Tenant;
use Database\Seeders\RolesAndPermissions;
use Database\Seeders\SetupSoftwareTablesSeeder;
use Illuminate\Foundation\Testing\TestCase as BaseTestCase;
use Laravel\Passport\Passport;
use Laravel\Sanctum\Sanctum;

abstract class TestCase extends BaseTestCase
{
    use CreatesApplication;

    public Employee $employee;
    public Tenant $tenant;

    protected function setUp(): void
    {
        parent::setUp();

        $this->seed([SetupSoftwareTablesSeeder::class, RolesAndPermissions::class]);

        $this->employee = Employee::factory()->create();

        $this->tenant = $this->employee->tenant;

        $this->createStartSubscription($this->tenant);
    }

    public function actingAsStartAdmin(Employee $employee = null): TestCase
    {
        $employee ??= $this->employee;

        $employee->assignRole(Role::firstWhere('name', 'start-admin'));

        Passport::actingAs($employee);

        return $this;
    }

    public function actingAsTenant(Tenant $tenant = null): TestCase
    {
        $tenant ??= $this->tenant;

        Sanctum::actingAs($tenant);

        return $this;
    }

    function createStartSubscription(Tenant $tenant): Subscription
    {
        return Subscription::createSubscription(
            tenant: $tenant,
            softwareData: [
                [
                    'software_code' => SoftwareCode::Start,
                    'software_package_code' => SoftwarePackageCode::Enterprise1,
                ],
            ]
        );
    }
}
