<?php

use App\Models\EmployeeManagerView;
use App\Models\Role;
use OwenIt\Auditing\Auditable as AuditableTrait;
use OwenIt\Auditing\Contracts\Auditable;

arch()->preset()->php();
arch()->preset()->security();
arch()->preset()->laravel();

test('models should implement Auditable interface and use Auditable trait')
    ->expect('App\Models')
    ->toImplement(Auditable::class)
    ->ignoring([EmployeeManagerView::class, Role::class])
    ->toUse(AuditableTrait::class);
